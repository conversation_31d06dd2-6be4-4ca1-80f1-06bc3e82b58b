package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.pagehelper.PageHelper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import net.armcloud.paas.manage.client.component.PadTaskComponent;
import net.armcloud.paas.manage.client.internal.dto.ExecuteADBFacadeDTO;
import net.armcloud.paas.manage.client.internal.dto.ResetDTO;
import net.armcloud.paas.manage.client.internal.dto.RestartDTO;
import net.armcloud.paas.manage.client.internal.dto.UpgradeImageDTO;
import net.armcloud.paas.manage.client.internal.dto.command.ExecuteADBCMDDTO;
import net.armcloud.paas.manage.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paas.manage.client.internal.stub.PadInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.DeviceGroupLevelVO;
import net.armcloud.paas.manage.client.internal.vo.GeneratePadTaskInfoVO;
import net.armcloud.paas.manage.client.internal.vo.PadGroupLevelVO;
import net.armcloud.paas.manage.mapper.paas.INetStorageMapper;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.IDeviceService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.constant.StatusConstant;
import net.armcloud.paas.manage.mapper.comms.ServerPadMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.mapper.paas.ScreenLayoutMapper;
import net.armcloud.paas.manage.service.IFileService;
import net.armcloud.paas.manage.service.IPadService;
import net.armcloud.paas.manage.service.IPadTaskService;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.dto.api.LimitBandwidthDTO;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadFileV2DTO;
import net.armcloud.paascenter.common.model.dto.api.PadUninstallAppFileDTO;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.NumberConsts.TWO;
import static net.armcloud.paas.manage.constant.TaskTypeConstants.EXECUTE_COMMAND;
import static net.armcloud.paas.manage.exception.code.BasicExceptionCode.PARAMETER_EXCEPTION;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.ADMIN_SYSTEM;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Service
public class PadServiceImpl implements IPadService {
    public static final Integer STORAGE_CONVERSION = 1073741824;
    private final PadMapper padMapper;

    private final IDeviceService iDeviceService;
    private final ServerPadMapper serverPadMapper;
    private final ApplicationContext applicationContext;
    private final ScreenLayoutMapper screenLayoutMapper;
    private final PadInternalFeignStub padInternalFeignStub;
    private final PadTaskComponent padTaskComponent;

    ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("pad-pool-%d").build();
    private final ExecutorService executor = new ThreadPoolExecutor(8, 16,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    @Override
    public Page<PadVO> listPads(PadDTO padDTO) {
        if (!SecurityUtils.isAdmin()) {
            padDTO.setCustomerId(SecurityUtils.getUserId());
        }

        String instanceIp = padDTO.getInstanceIp();
        if (!StringUtils.isEmpty(instanceIp) && instanceIp.contains(",")) {
            List<String> instanceIps = Arrays.stream(instanceIp.split(","))
                    .map(String::trim).collect(Collectors.toList());
            padDTO.setInstanceIps(instanceIps);
            padDTO.setInstanceIp(null);
        }

        Future<Integer> submit = executor.submit(() -> padMapper.countPads(padDTO));

        int skip = (padDTO.getPage() - 1) * padDTO.getRows();
        List<PadVO> padVOS = padMapper.listPads2(padDTO, skip, padDTO.getRows());

        SelectionScreenLayoutDTO param = new SelectionScreenLayoutDTO();
        List<SelectionScreenLayoutVO> screenLayouts = screenLayoutMapper.selectionList(param);
        HashMap<String, String> screenLayoutMap = new HashMap<>();
        if (isNotEmpty(screenLayouts)) {
            for (SelectionScreenLayoutVO screenLayout : screenLayouts) {
                String val = screenLayout.getScreenWidth() + "x" + screenLayout.getScreenHigh() + "px | " + screenLayout.getPixelDensity() + "dpi | " + screenLayout.getScreenRefreshRate() + "fps";
                screenLayoutMap.put(screenLayout.getCode(), val);
            }
        }

        List<String> padCodes = padVOS.stream().map(PadVO::getPadCode).filter(Objects::nonNull).collect(Collectors.toList());
        List<PublicIpDTO> publicIps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(padCodes)) {
            publicIps = serverPadMapper.getCommsServerIdByPadCode2(padCodes);
        }

        for (PadVO padVO : padVOS) {
            String padCode = padVO.getPadCode();
            if (StrUtil.isNotEmpty(padCode)) {
                //根据server_pad查询server表，找到对应的public_ip
                PublicIpDTO publicIpDTO = publicIps.stream().filter(e -> e.getPadCode().equals(padCode)).findFirst().orElse(null);
                padVO.setConnectionAddress(null != publicIpDTO ? publicIpDTO.getPublicIp() : null);

                //判断是否已分配客户
                if (padVO.getCustomerId() == null) {
                    padVO.setStatus(StatusConstant.NOT_ALLOCATED);
                    padVO.setStatusName(StatusConstant.NOT_ALLOCATED_NAME);
                } else {
                    padVO.setStatus(StatusConstant.ALLOCATED);
                    padVO.setStatusName(StatusConstant.ALLOCATED_NAME);
                }
                // 应用数量，上行限速，下行限速无数据来源。暂设设为0
                padVO.setAppCount(0);

                String dataSizeInfo = null;
                String netStorageDataSizeInfo = null;
                if(isNotEmpty(padVO.getNetStorageResApplySize())){
                    BigDecimal netStorageResApplySizeInGB = new BigDecimal(padVO.getNetStorageResApplySize());
                    netStorageDataSizeInfo = "0GB" + "/" + netStorageResApplySizeInGB + "GB";
                    if (isNotEmpty(padVO.getNetStorageResUseSize())) {
                        BigDecimal dataSizeUsedInGB = new BigDecimal(padVO.getNetStorageResUseSize());
                        netStorageDataSizeInfo = dataSizeUsedInGB + "GB" + "/" + netStorageResApplySizeInGB + "GB";
                    }
                    padVO.setNetStorageDataSizeInfo(netStorageDataSizeInfo);
                }
                if (isNotEmpty(padVO.getDataSize())) {
                    BigDecimal dataSizeInGB = new BigDecimal(padVO.getDataSize()).divide(new BigDecimal(STORAGE_CONVERSION), TWO, RoundingMode.HALF_UP);
                    dataSizeInfo = "0GB" + "/" + dataSizeInGB + "GB";
                    if (isNotEmpty(padVO.getDataSizeUsed())) {
                        BigDecimal dataSizeUsedInGB = new BigDecimal(padVO.getDataSizeUsed()).divide(new BigDecimal(STORAGE_CONVERSION), TWO, RoundingMode.HALF_UP);
                        dataSizeInfo = dataSizeUsedInGB + "GB" + "/" + dataSizeInGB + "GB";
                    }
                }
                padVO.setDataSizeInfo(dataSizeInfo);

                if (isNotEmpty(screenLayoutMap) && ObjectUtil.isNotEmpty(screenLayoutMap.get(padVO.getScreenLayout()))) {
                    padVO.setScreenLayoutInfo(screenLayoutMap.get(padVO.getScreenLayout()));
                }
            }
        }

        int total = 0;
        try {
            total = submit.get();
        } catch (Exception e) {
        }
        Page<PadVO> re = new Page<>(padVOS);
        re.setRows(padDTO.getRows());
        re.setPage(padDTO.getPage());
        double totalPage = Math.ceil((double) total / padDTO.getRows());
        re.setTotalPage((int) totalPage);
        re.setTotal(total);
        return re;
    }

    @Override
    public PadInfoVO getPadInfo(String padCode) {
        PadInfoVO padInfo = padMapper.getPadInfo(padCode);
        String dataSizeInfo = null;
        if (ObjectUtil.isNotNull(padInfo) && ObjectUtil.isNotEmpty(padInfo.getDataSize())) {
            BigDecimal dataSizeInGB = new BigDecimal(padInfo.getDataSize()).divide(new BigDecimal(STORAGE_CONVERSION), TWO, RoundingMode.HALF_UP);
            dataSizeInfo = "0GB" + "/" + dataSizeInGB + "GB";
            if (isNotEmpty(padInfo.getDataSizeUsed())) {
                BigDecimal dataSizeUsedInGB = new BigDecimal(padInfo.getDataSizeUsed()).divide(new BigDecimal(STORAGE_CONVERSION), TWO, RoundingMode.HALF_UP);
                dataSizeInfo = dataSizeUsedInGB + "GB" + "/" + dataSizeInGB + "GB";
            }
            padInfo.setDataSizeInfo(dataSizeInfo);
        }
        return padInfo;
    }

    @Override
    public List<PadStatusVO> padListStatus(List<String> padCodes) {
        return padMapper.padListStatus(padCodes);
    }

    /**
     * 根据armServerCode查询padCode列表
     *
     * @param armServerCode
     * @return
     */
    @Override
    public List<String> padCodesByArmServer(List<String> armServerCode) {
        return padMapper.getPadCodesByArmServer(armServerCode);
    }

    @Override
    public void allocatePad(PadGroupDTO param) {
        //批量修改数据
        padMapper.allocatePad(param);
    }

    @Override
    public void restart(List<String> padCodes) {
        RestartDTO restartDTO = new RestartDTO();
        restartDTO.setCustomerId(SecurityUtils.getUserId());
        restartDTO.setPadCodes(padCodes);
        restartDTO.setTaskSource(ADMIN_SYSTEM);
        FeignUtils.getContent(padInternalFeignStub.restart(restartDTO));
    }

    @Override
    public void reset(List<String> padCodes) {
        ResetDTO resetDTO = new ResetDTO();
        resetDTO.setCustomerId(SecurityUtils.getUserId());
        resetDTO.setPadCodes(padCodes);
        resetDTO.setTaskSource(ADMIN_SYSTEM);
        FeignUtils.getContent(padInternalFeignStub.reset(resetDTO));
    }

    @Override
    public List<GeneratePadTaskInfoVO> upgradeImage(UpgradeImageDTO dto) {
        dto.setTaskSource(ADMIN_SYSTEM);
        dto.setOprBy(ADMIN_SYSTEM.getCode());
        dto.setCustomerId(SecurityUtils.getUserId());
       return FeignUtils.getContent(padInternalFeignStub.upgradeImage(dto));
    }

    @Override
    public void asyncCmd(ExecuteADBFacadeDTO dto) {
        Long userId = SecurityUtils.getUserId();
        List<String> padCodes = dto.getPadCodes();
        PadCMDForwardDTO padCMDForwardDTO = new ExecuteADBCMDDTO()
                .setCmd(dto.getCommand())
                .builderForwardDTO(padCodes, ADMIN_SYSTEM);
        padTaskComponent.addPadTask(userId, padCodes, EXECUTE_COMMAND, null, JSON.toJSONString(padCMDForwardDTO), ADMIN_SYSTEM);
    }

    @Override
    public void limitBandwidth(LimitBandwidthDTO dto) {
        dto.setCustomerId(SecurityUtils.getUserId());
        dto.setTaskSource(ADMIN_SYSTEM);
        FeignUtils.getContent(padInternalFeignStub.limitBandwidth(dto));
    }


    @Override
    public void deployApp(List<PadDownloadAppFileDTO> param) {
        if (CollectionUtils.isEmpty(param)) {
            throw new BasicException(PARAMETER_EXCEPTION);
        }

        Map<String, Long> padCodeMaps = param.stream()
                .flatMap(dto -> dto.getPadCodes().stream())
                .collect(Collectors.groupingBy(padCode -> padCode, Collectors.counting()));
        IPadTaskService padTaskService = applicationContext.getBean(IPadTaskService.class);
        if (CollUtil.isNotEmpty(padCodeMaps)) {
            padCodeMaps.forEach((padCode, count) -> {
                Integer currentTaskCount = padTaskService.selectPadTaskByPadCode(padCode);
                int newTaskCount = count.intValue();
                if (currentTaskCount + newTaskCount > 10) {
                    throw new BasicException(210021, padCode + "当前已存在安装任务" + currentTaskCount + "个，只能安装" + Math.max(0, 10 - currentTaskCount) + "个");
                }
            });
        }

        param.forEach(item -> {
            // item.setCustomerId(SecurityUtils.getUserId());
            item.setTaskSource(ADMIN_SYSTEM);
            item.setOprBy(SecurityUtils.getUsername());
        });

        FeignUtils.getContent(padInternalFeignStub.installApp(param));
    }

    @Override
    public void uninstallApp(List<PadUninstallAppFileDTO> param) {
        param.forEach(item -> {
            item.setCustomerId(SecurityUtils.getUserId());
            item.setTaskSource(ADMIN_SYSTEM);
            item.setOprBy(SecurityUtils.getUsername());
        });

        FeignUtils.getContent(padInternalFeignStub.uninstallApp(param));
    }

    @Override
    public void uploadInstance(PadDownloadFileV2DTO param) {
        param.setTaskSource(ADMIN_SYSTEM);
        param.setOprBy(SecurityUtils.getUsername());
        param.setCustomerId(SecurityUtils.getUserId());
        FeignUtils.getContent(padInternalFeignStub.uploadFileV2(param));
    }

    @Override
    public void uploadFilePadAndInstall(List<PadDownloadFileV2DTO> param) {
        for (PadDownloadFileV2DTO dto : param) {
            dto.setCustomerId(SecurityUtils.getUserId());
            dto.setTaskSource(SourceTargetEnum.OPEN_PLATFORM);
            dto.setOprBy(SecurityUtils.getUsername());
            FeignUtils.getContent(padInternalFeignStub.consoleUploadFilePad(dto));
        }
    }

    @Override
    public void deployServerApp(DeployServerAppDTO param) {
        IFileService fileService = applicationContext.getBean(IFileService.class);
        FeignUtils.getContent(padInternalFeignStub.installApp(fileService.deployServerApp(param)));
    }

    @Override
    public void updatePadAdbOpenStatus(List<String> padCodes, Integer adbOpenStatus) {
        padMapper.updatePadAdbOpenStatus(padCodes, adbOpenStatus);
    }

    @Override
    public List<PadVO> exportListPads(PadDTO padDTO) {

        if (!SecurityUtils.isAdmin()) {
            padDTO.setCustomerId(SecurityUtils.getUserId());
        }
        List<PadVO> padVOS = padMapper.exportListPads(padDTO);

        SelectionScreenLayoutDTO param = new SelectionScreenLayoutDTO();
        List<SelectionScreenLayoutVO> screenLayouts = screenLayoutMapper.selectionList(param);
        HashMap<String, String> screenLayoutMap = new HashMap<>();
        if (isNotEmpty(screenLayouts)) {
            for (SelectionScreenLayoutVO screenLayout : screenLayouts) {
                String val = screenLayout.getScreenWidth() + "x" + screenLayout.getScreenHigh() + "px | " + screenLayout.getPixelDensity() + "dpi | " + screenLayout.getScreenRefreshRate() + "fps";
                screenLayoutMap.put(screenLayout.getCode(), val);
            }
        }

        padVOS.forEach(padVO -> {
            if (StrUtil.isNotEmpty(padVO.getPadCode())) {
                //根据server_pad查询server表，找到对应的public_ip
                padVO.setConnectionAddress(serverPadMapper.getCommsServerIdByPadCode(padVO.getPadCode()));
                //判断是否已分配客户
                if (padVO.getCustomerId() == null) {
                    padVO.setStatus(StatusConstant.NOT_ALLOCATED);
                    padVO.setStatusName(StatusConstant.NOT_ALLOCATED_NAME);
                } else {
                    padVO.setStatus(StatusConstant.ALLOCATED);
                    padVO.setStatusName(StatusConstant.ALLOCATED_NAME);
                }
                // 应用数量，上行限速，下行限速无数据来源。暂设设为0
                padVO.setAppCount(0);

                String dataSizeInfo = null;
                if (isNotEmpty(padVO.getDataSize())) {
                    BigDecimal dataSizeInGB = new BigDecimal(padVO.getDataSize()).divide(new BigDecimal(STORAGE_CONVERSION), TWO, RoundingMode.HALF_UP);
                    dataSizeInfo = "0GB" + "/" + dataSizeInGB + "GB";
                    if (isNotEmpty(padVO.getDataSizeUsed())) {
                        BigDecimal dataSizeUsedInGB = new BigDecimal(padVO.getDataSizeUsed()).divide(new BigDecimal(STORAGE_CONVERSION), TWO, RoundingMode.HALF_UP);
                        dataSizeInfo = dataSizeUsedInGB + "GB" + "/" + dataSizeInGB + "GB";
                    }
                }
                padVO.setDataSizeInfo(dataSizeInfo);
                if (isNotEmpty(screenLayoutMap) && ObjectUtil.isNotEmpty(screenLayoutMap.get(padVO.getScreenLayout()))) {
                    padVO.setScreenLayoutInfo(screenLayoutMap.get(padVO.getScreenLayout()));
                }
            }
        });
        padVOS.forEach(padVO -> {
            padVO.setImageDetail(padVO.getImageName()+":"+padVO.getImageVersion()+"("+padVO.getImageId()+")");
            padVO.setExportUploadSpeed(StringUtils.isBlank(padVO.getUploadSpeed())?"未限行":Double.parseDouble(padVO.getUploadSpeed())<=0?"未限行":padVO.getUploadSpeed()+"(Mbps)");
            padVO.setExportDownloadSpeed(StringUtils.isBlank(padVO.getDownloadSpeed())?"未限行":Double.parseDouble(padVO.getDownloadSpeed())<=0?"未限行":padVO.getDownloadSpeed()+"(Mbps)");
            DecimalFormat df = new DecimalFormat("0.00");  // 保留两位小数
            if(Objects.nonNull(padVO.getDataSize())){
                double gb = padVO.getDataSize() / (1024.0 * 1024.0 * 1024.0);
                padVO.setExportDataSize(df.format(gb)+"GB");
            }
            if(Objects.nonNull(padVO.getDataSizeUsed())){
                double sizeuser = padVO.getDataSizeUsed() / (1024.0 * 1024.0 * 1024.0);
                padVO.setExportDataSizeUsed( df.format(sizeuser)+"GB");
            }
            List<Object> list = null;
            if (StringUtils.isNotEmpty(padVO.getAppsJson())) {
                list = JSON.parseArray(padVO.getAppsJson(), Object.class);
            }
            padVO.setAppNumber(CollectionUtils.isEmpty(list)?0:list.size());
            padVO.setAppsJson(filterAppsJson(padVO.getAppsJson()));
        });
        return padVOS;
    }


    public List<PadVO> getNetPadSize( String deviceLevel, Long customerId ) {
        return padMapper.getNetPadSize(deviceLevel,customerId);
    }


    @Override
    public void netStorageResBootOff(NetStorageResBootDTO param) {
        NetWorkOffDTO workOffDTO = new NetWorkOffDTO();
        workOffDTO.setCustomerId(param.getCustomerId());
        workOffDTO.setPadCodes(param.getPadCodeList());
        FeignUtils.getContent(padInternalFeignStub.netStorageResBootOff(workOffDTO));
    }

    @Override
    public void netStorageResBootOn(NetStorageResBootDTO param) {
        NetWorkOffDTO workOffDTO = new NetWorkOffDTO();
        workOffDTO.setCustomerId(param.getCustomerId());
        workOffDTO.setPadCodes(param.getPadCodeList());
        FeignUtils.getContent(padInternalFeignStub.netStorageResBootOn(workOffDTO));
    }

    @Override
    public String netStorageResMoreCompatible(NetStorageResMoreCompatibleDTO param) {
        NetStorageResMoreCompatiblePaasDTO compatiblePaasDTO = new NetStorageResMoreCompatiblePaasDTO();
        compatiblePaasDTO.setPadCodes(param.getPadCodes());
        compatiblePaasDTO.setCustomerId(param.getCustomerId());
        compatiblePaasDTO.setStorageCapacity(param.getStorageCapacity());
        compatiblePaasDTO.setDeviceLevel(param.getDeviceLevel());
        FeignUtils.getContent(padInternalFeignStub.netStorageResMoreCompatible(compatiblePaasDTO));
        return StringUtils.EMPTY;
    }

    @Override
    public void netStorageResDelete(NetStorageResBootDTO param) {
        NetWorkOffDTO workOffDTO = new NetWorkOffDTO();
        workOffDTO.setCustomerId(param.getCustomerId());
        workOffDTO.setPadCodes(param.getPadCodeList());
        FeignUtils.getContent(padInternalFeignStub.netStorageResBootDelete(workOffDTO));
    }

    @Override
    public List<PadGroupLevelVO> padGroupDeviceLevel(NetStorageResDetailDTO netStorageResDetailDTO) {
        return padMapper.padGroupDeviceLevel(netStorageResDetailDTO);
    }

    @Resource
    private INetStorageMapper netStorageMapper;


    @Override
    public NetStorageResVo getNetStoragePadList(NetStorageResDetailDTO netStorageResDetailDTO) {
        List<PadVO> resultList = padMapper.getNetStoragePadList(netStorageResDetailDTO);
        NetStorageResVo netStorageResVo = new NetStorageResVo();
        // 已创建的实例数量
        netStorageResVo.setPadSize((long)resultList.size());
        //开机实例数量 18-关机
        long count = resultList.stream().filter(obj -> Objects.equals(obj.getPadStatus(), 10)).count();
        netStorageResVo.setOffPadSize(count);
        //购买网存的客户数量
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotEmpty(netStorageResDetailDTO.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,netStorageResDetailDTO.getClusterCode());
        }
        if(Objects.nonNull(netStorageResDetailDTO.getCustomerId())){
            wrapper.eq(NetStorageRes::getCustomerId,netStorageResDetailDTO.getCustomerId());
        }
        List<NetStorageRes> storageResList = netStorageMapper.selectList(wrapper);
        long uniqueCustomerCount = storageResList.stream()
                .map(NetStorageRes::getCustomerId)  // 提取 customerId
                .distinct()  // 去重
                .count();  // 统计数量
        netStorageResVo.setNetStorageResCustomerNum(uniqueCustomerCount);  // 设置到 Vo 对象中
        DeviceLevelDTO dto = new DeviceLevelDTO();

        dto.setDcCode(netStorageResDetailDTO.getDcCode());
        dto.setClusterCode(netStorageResDetailDTO.getClusterCode());
        if(Objects.nonNull(netStorageResDetailDTO.getCustomerId())){
            dto.setCustomerId(netStorageResDetailDTO.getCustomerId());
        }
//        dto.setCustomerId(netStorageResDetailDTO.getCustomerId());
        //获取板卡数量
        List<DeviceVO> deviceList = iDeviceService.getDeviceLevel(dto);
        //板卡数量
        long deviceSize = deviceList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .count();
        netStorageResVo.setCountDeviceTotal(deviceSize);

        //已售板卡
        long deviceCustomerSize = deviceList.stream()
                .map(DeviceVO::getCustomerId)
                .filter(Objects::nonNull)
                .count();
        netStorageResVo.setCountDeviceUsed(deviceCustomerSize);

//        long totalSize = resultList.stream()
//                .map(PadVO::getNetStorageResSize) // 提取 netStorageResSize
//                .filter(Objects::nonNull)         // 过滤掉 null 值
//                .mapToLong(Long::longValue)       // 转换为 long 类型
//                .sum();                           // 计算总和

        Map<String, Long> groupedData = deviceList.stream()
                .filter(d -> d.getDeviceLevel() != null)
                .collect(Collectors.groupingBy(
                        DeviceVO::getDeviceLevel,
                        Collectors.counting()
                ));


        // 构建 List<DeviceGroupLevelVO> 对象
        List<DeviceGroupLevelVO> deviceGroupLevelList = groupedData.entrySet().stream()
                .map(entry -> {
                    DeviceGroupLevelVO vo = new DeviceGroupLevelVO();
                    vo.setDeviceLevel(entry.getKey()); // 设置 deviceLevel
                    vo.setNumber(entry.getValue());     // 设置数量
                    return vo;
                })
                .collect(Collectors.toList());
        long storageCapacityUsed = storageResList.stream()
                .mapToLong(NetStorageRes::getStorageCapacityUsed)  // 转换为 double 流
                .sum();  // 对流中的所有元素求和
        netStorageResVo.setDeviceGroupLevelVOList(deviceGroupLevelList);

        netStorageResVo.setStorageCapacityUsed(storageCapacityUsed);
        return netStorageResVo;
    }

    private  String filterAppsJson(String appsJson) {
        try {
            // 判断是否为空或空字符串
            if (appsJson == null || appsJson.isEmpty()) {
                return "";  // 返回空字符串
            }
            ObjectMapper objectMapper = new ObjectMapper();
            // 解析 JSON 字符串
            JsonNode rootNode = objectMapper.readTree(appsJson);
            if (!rootNode.isArray()) {
                return ""; // 确保 appsJson 是数组格式
            }

            List<JsonNode> filteredApps = new ArrayList<>();
            for (JsonNode appNode : rootNode) {
                // 创建新的 JSON 对象，只包含 appName 和 packageName 字段
                ObjectNode filteredNode = objectMapper.createObjectNode();
                if (appNode.has("packageName")) {
                    filteredNode.set("packageName", appNode.get("packageName"));
                }
                if (appNode.has("versionName")) {
                    filteredNode.set("versionName", appNode.get("versionName"));
                }

                filteredApps.add(filteredNode);
            }

            // 转换为 JSON 字符串
            ArrayNode filteredArrayNode = objectMapper.valueToTree(filteredApps);
            return objectMapper.writeValueAsString(filteredArrayNode);
        } catch (Exception e) {
            e.printStackTrace();
            return ""; // 出现异常时返回空字符串
        }
    }
    public PadServiceImpl(PadMapper padMapper, ServerPadMapper serverPadMapper, ApplicationContext applicationContext, ScreenLayoutMapper screenLayoutMapper,
                          IDeviceService iDeviceService,
                          PadInternalFeignStub padInternalFeignStub, PadTaskComponent padTaskComponent) {
        this.padMapper = padMapper;
        this.serverPadMapper = serverPadMapper;
        this.applicationContext = applicationContext;
        this.screenLayoutMapper = screenLayoutMapper;
        this.padInternalFeignStub = padInternalFeignStub;
        this.padTaskComponent = padTaskComponent;
        this.iDeviceService = iDeviceService;
    }
}
