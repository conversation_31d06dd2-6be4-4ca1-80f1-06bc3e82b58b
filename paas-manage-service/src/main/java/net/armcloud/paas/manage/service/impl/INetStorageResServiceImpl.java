package net.armcloud.paas.manage.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.GroupNetPadByDeviceLevelDTO;
import net.armcloud.paas.manage.client.internal.stub.PadInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.NetPadDeviceVO;
import net.armcloud.paas.manage.client.internal.vo.PadGroupLevelVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.mapper.paas.EdgeClusterMapper;
import net.armcloud.paas.manage.mapper.paas.INetStorageMapper;
import net.armcloud.paas.manage.model.dto.DeviceLevelDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.dto.NetWorkVirtualizeManageDTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.IDeviceService;
import net.armcloud.paas.manage.service.INetStorageResService;
import net.armcloud.paas.manage.service.IPadService;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.utils.StringUtils;
import net.armcloud.paascenter.common.model.dto.api.NetWorkVirtualizeDTO;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class INetStorageResServiceImpl extends ServiceImpl<INetStorageMapper, NetStorageRes> implements INetStorageResService {

    @Autowired
    private IPadService iPadService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    EdgeClusterMapper edgeClusterMapper;



    @Override
    public Boolean save(NetStorageResDTO param) {
        log.info("INetStorageResServiceImpl_save:{}", JSON.toJSONString(param));
        NetStorageRes storageRes = param.buildNetStorageRes();
         LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        NetStorageRes one = this.getOne(wrapper);
        //能在该集群下找到该用户的分配记录,直接累加进去
        if(Objects.nonNull(one)){
            one.setStorageCapacity(one.getStorageCapacity() + param.getStorageCapacity());
            one.setUpdateBy(param.getCreateBy());
            this.updateById(one);
            return true;
        }
        //找不到就新建
        storageRes.setCreateTime(new Date());
        return this.save(storageRes);
    }

    @Override
    public Boolean unsubscribe(NetStorageResDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getNetStorageResId, param.getNetStorageResId());
        NetStorageRes storageRes = this.getOne(wrapper);

        if(Objects.isNull(storageRes)){
            throw  new RuntimeException("退订数量大于可用数量");
        }

        if(storageRes.getStorageCapacity()< param.getStorageCapacity()){
            throw  new RuntimeException("退订数量大于可用数量");
        }
        //TODO 计算剩余容量
        storageRes.setStorageCapacity(storageRes.getStorageCapacity() - param.getStorageCapacity());
        return this.updateById(storageRes);
    }

    @Override
    public NetStorageResVo getDetailByCustomerId(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(!Objects.isNull(param.getCustomerId())){
            wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        }
        if(!StringUtils.isEmpty(param.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        }
        if(!StringUtils.isEmpty(param.getDcCode())){
            wrapper.eq(NetStorageRes::getDcCode,param.getDcCode());
        }
        Long storageCapacityTotal = edgeClusterMapper.selectAllStorageCapacity(param);
        List<NetStorageRes> list = this.list(wrapper);
        //没有数据,全部返回0
        if(CollectionUtils.isEmpty(list)){
            NetStorageResVo resVo = new NetStorageResVo(0L);
            resVo.setStorageCapacityTotal(storageCapacityTotal);
            return resVo;
        }
        long totalStorageCapacity = list.stream()
                .mapToLong(NetStorageRes::getStorageCapacity)
                .sum();
        NetStorageResVo netStorageResVo = iPadService.getNetStoragePadList(param);
        netStorageResVo.setStorageCapacityUsed(totalStorageCapacity);
        netStorageResVo.setStorageCapacityTotal(storageCapacityTotal);
        return netStorageResVo;
    }

    @Override
    public Page<NetStorageResListVo> getDetailList(NetStorageResDetailDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<NetStorageResListVo> netStoragePadList = this.baseMapper.getNetStoragePadList(param);
        if(CollectionUtils.isEmpty(netStoragePadList)){
            return new Page<>(netStoragePadList);
        }

        netStoragePadList.parallelStream().forEach(netStorageResListVo -> {
            NetStorageResListVo padSize = this.baseMapper.getPadSize(netStorageResListVo.getNetStorageResId());
            String applySize = this.baseMapper.getNetStorageResApplySize(netStorageResListVo.getCustomerId());
            netStorageResListVo.setApplySize(applySize);
            netStorageResListVo.setPadSize(padSize.getPadSize());
            netStorageResListVo.setOffPadSize(padSize.getOffPadSize());

        });
        return new Page<>(netStoragePadList);

    }

    @Override
    public Page<PadVO> getPadCodeDetailList(NetStorageResDetailDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<PadVO> list = this.baseMapper.getPadCodeDetailList(param.getCustomerId());
        return new Page<>(list);
    }

    @Override
    public StorageCapacityDetailVO getDetailStorageCapacityAvailable(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(!SecurityUtils.isAdmin() || param.isGetTheCurrentUserFlag()){
            wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        }
        if(StringUtils.isNotBlank(param.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        }
        List<NetStorageRes> list = this.list(wrapper);
        //没有可用资源,并且不是管理员,返回空对象
        if(CollectionUtils.isEmpty(list) &&!SecurityUtils.isAdmin()){
            return new StorageCapacityDetailVO();
        }
        //TODO 管理员查询不应该混用一个接口. 应该走查询可用总量接口
        if(SecurityUtils.isAdmin() &&!param.isGetTheCurrentUserFlag()){
            NetStorageResDetailDTO detailVO = new NetStorageResDetailDTO();
            detailVO.setDcCode(param.getDcCode());
            detailVO.setClusterCode(param.getClusterCode());
            //总容量
            Long capacity = edgeClusterMapper.selectAllStorageCapacity(detailVO);
            //已分配容量
            long sum = list.stream().mapToLong(NetStorageRes::getStorageCapacity).sum();
            StorageCapacityDetailVO result = new StorageCapacityDetailVO();
            result.setStorageCapacityAvailable(capacity - sum);
            return result;
        }

        //有可用资源,但是是管理员,所有的可用资源大小

        long sum = list.stream()
                .mapToLong(netStorageRes -> {
                    // 获取 storageCapacity 和 storageCapacityUsed，处理 null 值为 0
                    Long storageCapacity = Optional.ofNullable(netStorageRes.getStorageCapacity()).orElse(0L);
                    Long storageCapacityUsed = Optional.ofNullable(netStorageRes.getStorageCapacityUsed()).orElse(0L);
                    long result = storageCapacity - storageCapacityUsed;

                    // 如果计算结果为负数，打印日志并设为 0
                    if (result < 0) {
                        log.warn("Negative storage difference for NetStorageRes with ID {}: storageCapacity = {}, storageCapacityUsed = {}, result = {}",
                                netStorageRes.getNetStorageResId(), storageCapacity, storageCapacityUsed, result);
                        return 0L;
                    }
                    return result;
                })
                .sum();
        StorageCapacityDetailVO storageCapacityDetailVO = new StorageCapacityDetailVO();
         storageCapacityDetailVO.setStorageCapacityAvailable(sum);
         return storageCapacityDetailVO;
    }

    @Autowired
    private PadInternalFeignStub padInternalFeignStub;
    @Override
    public String virtualizeDevice(NetWorkVirtualizeManageDTO param) {
        DeviceLevelDTO dto = new DeviceLevelDTO();
        dto.setCustomerId(param.getCustomerId());
        dto.setDeviceLevel(param.getSpecificationCode());
        //拥有的板卡信息
        List<DeviceVO> deviceLevel = deviceService.getDeviceLevel(dto);
        NetWorkVirtualizeDTO virtualizeDTO = new NetWorkVirtualizeDTO();
        if(Objects.nonNull(param.getTemplateId())){
            virtualizeDTO.setRealPhoneTemplateId((long)param.getTemplateId());
        }
        virtualizeDTO.setCustomerId(param.getCustomerId());
        virtualizeDTO.setClusterCode(param.getClusterCode());
        virtualizeDTO.setSocModel(param.getSocModel());
        virtualizeDTO.setSpecificationCode(param.getSpecificationCode());
        virtualizeDTO.setImageId(param.getImageId());
        virtualizeDTO.setScreenLayoutCode(param.getScreenLayoutCode());
        virtualizeDTO.setIsolateCpu(param.getIsolateCpu());
        virtualizeDTO.setIsolateMemory(param.getIsolateMemory());
        virtualizeDTO.setIsolateStorage(param.getIsolateStorage());
        virtualizeDTO.setNumber(param.getNumber());
        virtualizeDTO.setNetStorageResFlag(1);
        virtualizeDTO.setDns(param.getDns());
        virtualizeDTO.setRandomADITemplates(param.getRandomADITemplates());
        virtualizeDTO.setAdiUrl(param.getAdiUrl());
        virtualizeDTO.setAdiPassword(param.getAdiPassword());
        virtualizeDTO.setDeviceAndroidProps(param.getDeviceAndroidProps());
        virtualizeDTO.setPadType(param.getPadType());
        virtualizeDTO.setStorageSize(param.getStorageSize());
        FeignUtils.getContent(padInternalFeignStub.virtualizeNetStorageRes(virtualizeDTO));
        //拥有的实例数量
        List<PadVO> padSize = iPadService.getNetPadSize(param.getSpecificationCode(), param.getCustomerId());
        //TODO 创建板卡待实现
//        String format = String.format("当前可用的 %s 规格板卡数量: %s, 实例总数:%s .如需调整,可前往板卡列表页,重新设置规格", param.getSpecificationCode(), deviceLevel.size(), padSize.size());
        String format = "实例创建成功";
        return format;

    }

    @Override
    public NetStorageResVo userGetDetailByCustomerId(NetStorageResDetailDTO param) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        if(!Objects.isNull(param.getCustomerId())){
            wrapper.eq(NetStorageRes::getCustomerId,param.getCustomerId());
        }
        if(!StringUtils.isEmpty(param.getClusterCode())){
            wrapper.eq(NetStorageRes::getClusterCode,param.getClusterCode());
        }
        if(!StringUtils.isEmpty(param.getDcCode())){
            wrapper.eq(NetStorageRes::getDcCode,param.getDcCode());
        }
        Long storageCapacityTotal = edgeClusterMapper.selectAllStorageCapacity(param);
        List<NetStorageRes> list = this.list(wrapper);
        //没有数据,全部返回0
        if(CollectionUtils.isEmpty(list)){
            NetStorageResVo resVo = new NetStorageResVo(0L);
            //管理员展示总容量
            if (SecurityUtils.isAdmin()) {
                resVo.setStorageCapacityTotal(storageCapacityTotal);
            }
            return resVo;
        }
        long totalStorageCapacity = list.stream()
                .mapToLong(NetStorageRes::getStorageCapacity)
                .sum();
        NetStorageResVo netStorageResVo = iPadService.getNetStoragePadList(param);
        //总拥有资源
                netStorageResVo.setStorageCapacityTotal(totalStorageCapacity);
        GroupNetPadByDeviceLevelDTO deviceLevelDTO = new GroupNetPadByDeviceLevelDTO();
        deviceLevelDTO.setCustomerId(param.getCustomerId());
        deviceLevelDTO.setClusterCode(param.getClusterCode());
        List<NetPadDeviceVO> netPadDeviceVOList = FeignUtils.getContent(padInternalFeignStub.groupNetPadByDeviceLevel(deviceLevelDTO));
        netStorageResVo.setNetPadDeviceVOList(netPadDeviceVOList);
        List<PadGroupLevelVO> padGroupLevelVOS = iPadService.padGroupDeviceLevel(param);
        netStorageResVo.setPadGroupLevelVOList(padGroupLevelVOS);
        Long customerId = param.getCustomerId();
        if (SecurityUtils.isAdmin()) {
            customerId = null;
        }
        netStorageResVo.setApplySize(this.baseMapper.getNetStorageResApplySize(customerId));
        return netStorageResVo;
    }
}
