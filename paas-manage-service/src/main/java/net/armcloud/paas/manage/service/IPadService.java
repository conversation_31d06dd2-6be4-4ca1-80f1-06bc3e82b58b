package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.client.internal.dto.ExecuteADBFacadeDTO;
import net.armcloud.paas.manage.client.internal.dto.UpgradeImageDTO;
import net.armcloud.paas.manage.client.internal.vo.GeneratePadTaskInfoVO;
import net.armcloud.paas.manage.client.internal.vo.PadGroupLevelVO;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.NetStorageResVo;
import net.armcloud.paas.manage.model.vo.PadInfoVO;
import net.armcloud.paas.manage.model.vo.PadStatusVO;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadFileV2DTO;
import net.armcloud.paascenter.common.model.dto.api.PadUninstallAppFileDTO;
import net.armcloud.paascenter.common.model.dto.api.LimitBandwidthDTO;

import java.util.List;
public interface IPadService {
    /**
     * 实例列表
     */
    Page<PadVO> listPads(PadDTO padDTO);

    PadInfoVO getPadInfo(String padCode);

    List<PadStatusVO> padListStatus(List<String> padCodes);

    /**
     * 根据服务器编码查询实例编码
     */
    List<String> padCodesByArmServer(List<String> armServerCode);

    void allocatePad(PadGroupDTO param);

    void restart(List<String> padCodes);

    void reset(List<String> padCodes);

    List<GeneratePadTaskInfoVO> upgradeImage(UpgradeImageDTO upgradeImageDTO);

    void asyncCmd(ExecuteADBFacadeDTO dto);

    void limitBandwidth(LimitBandwidthDTO dto);

    void deployApp(List<PadDownloadAppFileDTO> param);

    void uninstallApp(List<PadUninstallAppFileDTO> param);

    void uploadInstance(PadDownloadFileV2DTO param);

    void uploadFilePadAndInstall(List<PadDownloadFileV2DTO> param);

    void deployServerApp(DeployServerAppDTO param);

    /**
     * 修改pad表开启关闭adb状态
     * @param padCode
     * @param adbOpenStatus
     */
    void updatePadAdbOpenStatus(List<String> padCode,Integer adbOpenStatus);

    List<PadVO> exportListPads(PadDTO param);

    NetStorageResVo getNetStoragePadList(NetStorageResDetailDTO netStorageResDetailDTO);

    /**
     * 获取网络存储的pad数量
     * @param deviceLevel
     * @param customerId
     * @return
     */
    List<PadVO> getNetPadSize( String deviceLevel, Long customerId );


    /**
     * 网络存储资源开关机
     * @param param
     */
    void netStorageResBootOff(NetStorageResBootDTO param);

    void netStorageResBootOn(NetStorageResBootDTO param);

    String netStorageResMoreCompatible(NetStorageResMoreCompatibleDTO param);

    /**
     * 网存资源回收
     * @param param
     */
    void netStorageResDelete(NetStorageResBootDTO param);

    List<PadGroupLevelVO> padGroupDeviceLevel(NetStorageResDetailDTO netStorageResDetailDTO);


}
