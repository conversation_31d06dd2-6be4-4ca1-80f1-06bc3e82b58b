package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.model.dto.CustomerDTO;
import net.armcloud.paas.manage.model.vo.CustomerCallbackVO;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.service.ICustomerCallbackService;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "客户管理")
public class CustomerController{

    @Resource
    private ICustomerService customerService;

    @Resource
    private ICustomerCallbackService customerCallbackService;

    @RequestMapping(value = "/manage/open/customer/add", method = RequestMethod.POST)
    @ApiOperation(value = "新增用户",httpMethod = "POST",notes = "新增用户")
    public Result<CustomerVO> addCustomer(@RequestBody @Valid CustomerDTO param){
        return customerService.insert(param);
    }

    @RequestMapping(value = "/manage/open/customer/update", method = RequestMethod.POST)
    @ApiOperation(value = "更新用户" ,httpMethod = "POST",notes = "更新用户")
    public Result<?> updateCustomer(@RequestBody @Valid CustomerDTO param) {
        return customerService.updateByPrimaryKey(param);
    }

    @RequestMapping(value = "/manage/open/customer/delete", method = RequestMethod.GET)
    @ApiOperation(value = "删除用户" ,httpMethod = "GET",notes = "删除用户")
    public Result<?> deleteCustomer(Long id) {
        customerService.deleteByPrimaryKey(id);
        return Result.ok();
    }
    @RequestMapping(value = "/manage/open/customer/enable", method = RequestMethod.GET)
    @ApiOperation(value = "启用用户" ,httpMethod = "GET",notes = "启用用户")
    public Result<?> enableCustomer(Long id) {
        customerService.enableCustomer(id);
        return Result.ok();
    }

    @RequestMapping(value = "/manage/open/customer/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉客户列表" ,httpMethod = "POST",notes = "下拉客户列表")
    public Result<List<CustomerVO>> selectionListCustomer(@RequestBody CustomerDTO param) {
        return Result.ok(customerService.selectionListCustomer(param));
    }

    @RequestMapping(value = "/manage/open/customer/list", method = RequestMethod.POST)
    @ApiOperation(value = "用户列表" ,httpMethod = "POST",notes = "用户列表")
    public Result<Page<CustomerVO>> listCustomer(@RequestBody CustomerDTO param) {
        Page<CustomerVO> customerVoPage = customerService.selectPageList(param);
        return Result.ok(customerVoPage);
    }

    @RequestMapping(value = "/manage/open/customer/detail", method = RequestMethod.GET)
    @ApiOperation(value = "用户详情" ,httpMethod = "GET",notes = "用户详情")
    public Result<CustomerVO> detailCustomer(Long id) {
        CustomerVO customerVO = customerService.selectByPrimaryKey(id);
        return Result.ok(customerVO);
    }

    @RequestMapping(value = "/manage/open/customer/callback", method = RequestMethod.GET)
    @ApiOperation(value = "查看回调配置" ,httpMethod = "GET",notes = "查看回调配置")
    public Result<List<CustomerCallbackVO>> callbackCustomer(Long id) {
        List<CustomerCallbackVO> customerCallbackVOS = customerCallbackService.selectByCustomerIdList(id);
        return Result.ok(customerCallbackVOS);
    }

    @RequestMapping(value = "/manage/open/customer/resetPassword", method = RequestMethod.GET)
    @ApiOperation(value = "重置密码" ,httpMethod = "GET",notes = "重置密码")
    public Result<?> resetPassword(Long id) {
        String pwd = customerService.resetPassword(id);
        return Result.ok(pwd);
    }

    @RequestMapping(value = "/manage/open/customer/getCustomerSelection", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户选择数据" ,httpMethod = "GET",notes = "获取用户选择数据")
    public Result<?> getCustomerSelection() {
        return Result.ok(customerService.getCustomerSelection());
    }

}
