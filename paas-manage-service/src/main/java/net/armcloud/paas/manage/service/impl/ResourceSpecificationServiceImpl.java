package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.mapper.paas.ResourceSpecificationMapper;
import net.armcloud.paas.manage.mapper.paas.SocModelMapper;
import net.armcloud.paas.manage.model.dto.QueryResourceSpecificationDTO;
import net.armcloud.paas.manage.model.dto.SelectionResourceSpecificationDTO;
import net.armcloud.paas.manage.model.vo.ResourceSpecificationVO;
import net.armcloud.paas.manage.model.vo.SelectionResourceSpecificationVO;
import net.armcloud.paas.manage.model.vo.SocModelVO;
import net.armcloud.paas.manage.service.IResourceSpecificationService;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static net.armcloud.paas.manage.constant.NumberConsts.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

/**
 * <p>
 * 实例规格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Service
public class ResourceSpecificationServiceImpl extends ServiceImpl<ResourceSpecificationMapper, ResourceSpecification> implements IResourceSpecificationService {

    @Resource
    private SocModelMapper socModelMapper;

    @Resource
    private PadMapper padMapper;

    @Override
    public List<SelectionResourceSpecificationVO> selectionList(SelectionResourceSpecificationDTO param) {
        return this.baseMapper.selectionList(param);
    }

    @Override
    public Page<ResourceSpecificationVO> selectList(QueryResourceSpecificationDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<ResourceSpecificationVO> list = this.baseMapper.listResourceSpecification(param);
        if (CollUtil.isNotEmpty(list)) {
            for (ResourceSpecificationVO resourceSpecification : list) {
                int count = padMapper.getCountByDeviceLevel(resourceSpecification.getSpecificationCode());
                resourceSpecification.setPadUse(count > ZERO);
            }
        }
        return new Page<>(list);
    }

    @Override
    public Result<?> addResourceSpecification(ResourceSpecification resourceSpecification) {
        SocModelVO socModelVO = socModelMapper.detailSocModel(resourceSpecification.getSocModel());
        if (isEmpty(socModelVO)) {
            return Result.fail("SoC型号不存在");
        }
        int padNumber = resourceSpecification.getPadNumber();
        double rawCpu = socModelVO.getVCpu()
                .multiply(new BigDecimal(ONE_THOUSAND))
                .divide(new BigDecimal(padNumber), 2, RoundingMode.HALF_UP)  // 保留2位小数
                .doubleValue();
        double rawMemory = (double) socModelVO.getMemory() / padNumber;
        double rawStorage = (double) socModelVO.getStorage() / padNumber;
        // 百位取整小数
        int cpu = (int) Math.round(rawCpu); // 四舍五入到最近的整数

        // 向下取整小数
        int memory = (int) Math.floor(rawMemory); // 向下取整
        int storage = (int) Math.floor(rawStorage); // 向下取整
        if (cpu < resourceSpecification.getCpu() || memory < resourceSpecification.getMemory() || storage < resourceSpecification.getStorage()) {
            return Result.fail("实例规格性能参数计算不对");
        }
        return Result.ok(this.save(resourceSpecification));
    }

    @Override
    public Result<?> deleteResourceSpecification(Long id) {
        ResourceSpecification specification = this.getById(id);
        int count = padMapper.getCountByDeviceLevel(specification.getSpecificationCode());
        if (count > ZERO) {
            return Result.fail("该实例规格下有应用实例，不能删除");
        }

        ResourceSpecification par = new ResourceSpecification();
        par.setId(id);
        par.setDeleteFlag(ONE);
        par.setUpdateTime(new Date());
        return this.updateById(par) ? Result.ok() : Result.fail("删除失败");
    }

    @Override
    public ResourceSpecificationVO detail(Long id) {
        ResourceSpecificationVO resourceSpecification = this.baseMapper.detailResourceSpecification(id);
        if (isNotEmpty(resourceSpecification)) {
            int count = padMapper.getCountByDeviceLevel(resourceSpecification.getSpecificationCode());
            resourceSpecification.setPadUse(count > ZERO);
        }
        return resourceSpecification;
    }

    @Override
    public Result<?> updateResourceSpecification(ResourceSpecification specification) {
        int count = padMapper.getCountByDeviceLevel(specification.getSpecificationCode());
        if (count > ZERO) {
            ResourceSpecification par = new ResourceSpecification();
            par.setId(specification.getId());
            par.setRemarks(specification.getRemarks());
            return this.baseMapper.updateScreenLayout(par) > ZERO ? Result.ok() : Result.fail("修改失败");
        }

        SocModelVO socModelVO = socModelMapper.detailSocModel(specification.getSocModel());
        if (isEmpty(socModelVO)) {
            return Result.fail("SoC型号不存在");
        }
        int padNumber = specification.getPadNumber();
        double rawCpu = socModelVO.getVCpu()
                .multiply(new BigDecimal(ONE_THOUSAND))
                .divide(new BigDecimal(padNumber), 2, RoundingMode.HALF_UP)  // 保留2位小数
                .doubleValue();
        double rawMemory = (double) socModelVO.getMemory() / padNumber;
        double rawStorage = (double) socModelVO.getStorage() / padNumber;
        // 百位取整小数
        int cpu = (int) Math.round(rawCpu); // 四舍五入到最近的整数

        // 向下取整小数
        int memory = (int) Math.floor(rawMemory); // 向下取整
        int storage = (int) Math.floor(rawStorage); // 向下取整
        if (cpu < specification.getCpu() || memory < specification.getMemory() || storage < specification.getStorage()) {
            return Result.fail("实例规格性能参数计算不对");
        }
        return this.baseMapper.updateScreenLayout(specification) > ZERO ? Result.ok() : Result.fail("修改失败");
    }
}
