package net.armcloud.paas.manage.controller;


import net.armcloud.paas.manage.constant.TaskTypeAndChannelEnum;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.constant.StatusConstant;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.model.dto.CancelPadTaskDTO;
import net.armcloud.paas.manage.model.dto.TaskDTO;
import net.armcloud.paas.manage.model.dto.TaskStaticDTO;
import net.armcloud.paas.manage.model.dto.TaskStaticExportDTO;
import net.armcloud.paas.manage.model.vo.FailPadDetailVo;
import net.armcloud.paas.manage.model.vo.SuccessTaskStaticVo;
import net.armcloud.paas.manage.model.vo.TaskStatisticVO;
import net.armcloud.paas.manage.model.vo.TaskVO;
import net.armcloud.paas.manage.service.ICustomerService;
import net.armcloud.paas.manage.service.ITaskService;
import net.armcloud.paas.manage.utils.ExportUtils;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@RestController
@RequestMapping()
@Api(tags = "任务管理")
public class TaskController {

    @Resource
    private ITaskService taskService;
    @Resource
    private ICustomerService customerService;

    @ApiOperation(value = "实例任务", httpMethod = "POST", notes = "实例任务")
    @RequestMapping(value = "/manage/open/task/instanceTask")
    public Result<Page<TaskVO>> instanceTask(@RequestBody TaskDTO param) {
        TaskDTO taskDTO = taskService.taskCustomerSearch(param);
        if(null==taskDTO){
            return Result.ok(new Page<TaskVO>());
        }
        taskDTO.setAllTaskTypes(StatusConstant.POD_TASK_TYPES);
        Page<TaskVO> taskVOPage = taskService.listTasks(taskDTO);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "应用任务", httpMethod = "POST", notes = "应用任务")
    @RequestMapping(value = "/manage/open/task/appTask")
    public Result<Page<TaskVO>> appTask(@RequestBody TaskDTO param) {
        TaskDTO taskDTO = taskService.taskCustomerSearch(param);
        if(null==taskDTO){
            return Result.ok(new Page<TaskVO>());
        }
        taskDTO.setAllTaskTypes(StatusConstant.APP_TASK_TYPES);
        Page<TaskVO> taskVOPage = taskService.listApps(taskDTO);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "文件上传实例任务", httpMethod = "POST", notes = "文件上传实例任务")
    @RequestMapping(value = "/manage/open/task/uploadInstanceTask")
    public Result<Page<TaskVO>> uploadInstanceTask(@RequestBody TaskDTO param) {
        TaskDTO taskDTO = taskService.taskCustomerSearch(param);
        if(null==taskDTO){
            return Result.ok(new Page<TaskVO>());
        }
        taskDTO.setAllTaskTypes(StatusConstant.FILE_TASK_TYPES);
        Page<TaskVO> taskVOPage = taskService.uploadListTasks(taskDTO);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "板卡任务", httpMethod = "POST", notes = "板卡任务")
    @RequestMapping(value = "/manage/open/task/deviceTask")
    public Result<Page<TaskVO>> deviceTask(@RequestBody TaskDTO param) {
        TaskDTO taskDTO = taskService.taskCustomerSearch(param);
        if(null==taskDTO){
            return Result.ok(new Page<TaskVO>());
        }
        taskDTO.setAllTaskTypes(StatusConstant.DEVICE_TASK_TYPES);
        Page<TaskVO> taskVOPage = taskService.listDeviceTasks(taskDTO);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "取消实例任务", httpMethod = "POST", notes = "取消实例任务")
    @RequestMapping(value = "/manage/open/task/cancelInstanceTask")
    public Result<Page<TaskVO>> cancelPadTask(@RequestBody CancelPadTaskDTO param) {
        return taskService.cancelPadTask(param) ? Result.ok() : Result.fail("取消实例任务失败");
    }

    @ApiOperation(value = "取消板卡任务", httpMethod = "POST", notes = "取消板卡任务")
    @RequestMapping(value = "/manage/open/task/cancelDeviceTask")
    public Result<Page<TaskVO>> cancelDeviceTask(@RequestBody CancelPadTaskDTO param) {
        return taskService.cancelDeviceTask(param) ? Result.ok() : Result.fail("取消板卡任务失败");
    }

    @ApiOperation(value = "任务统计", httpMethod = "POST", notes = "任务统计")
    @RequestMapping(value = "/manage/open/task/taskStatistic")
    public Result<TaskStatisticVO> taskStatistic() {
        return taskService.taskStatistic();
    }

    @ApiOperation(value = "成功任务统计", httpMethod = "POST", notes = "任务统计")
    @PostMapping(value = "/manage/open/task/success_task_static")
    public Result<List<SuccessTaskStaticVo>> successTaskStatic(@RequestBody TaskStaticDTO dto) {
        if(!SecurityUtils.isAdmin()){
            dto.setCustomerId(SecurityUtils.getUserId());
        }
        try {
//            PageHelper.startPage(dto.getPage(), dto.getRows());

            dto.setStatusList(Collections.singletonList("3"));
            return Result.ok(taskService.successTaskStatic(dto));
        }catch (Exception e){
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "成功任务统计导出", httpMethod = "POST", notes = "成功任务统计导出")
    @PostMapping(value = "/manage/open/task/export/success_task_static")
    public void exportSuccessTaskStatic(@RequestBody TaskStaticExportDTO dto, HttpServletResponse response) {
        TaskStaticDTO staticDTO = new TaskStaticDTO();
        staticDTO.setStartTime(dto.getStartTime());
        staticDTO.setEndTime(dto.getEndTime());
        if(!SecurityUtils.isAdmin()){
            staticDTO.setCustomerId(SecurityUtils.getUserId());
        }
        staticDTO.setStatusList(Collections.singletonList("3"));
        List<SuccessTaskStaticVo> successTaskStaticVos = taskService.successTaskStatic(staticDTO);
            ExportUtils.HttpExport(response,SuccessTaskStaticVo.class,successTaskStaticVos,"成功任务统计","成功任务统计");
    }


    @ApiOperation(value = "等待中任务统计", httpMethod = "POST", notes = "任务统计")
    @PostMapping(value = "/manage/open/task/waiting_task_static")
    public Result<List<SuccessTaskStaticVo>> waitingTaskStatic(@RequestBody TaskStaticDTO dto) {
        try {
            if(!SecurityUtils.isAdmin()){
                dto.setCustomerId(SecurityUtils.getUserId());
            }
//            PageHelper.startPage(dto.getPage(), dto.getRows());
            dto.setStatusList(Collections.singletonList("1"));
            return Result.ok(taskService.successTaskStatic(dto));
        }catch (Exception e){
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "进行中任务统计", httpMethod = "POST", notes = "任务统计")
    @PostMapping(value = "/manage/open/task/process_task_static")
    public Result<List<SuccessTaskStaticVo>> processTaskStatic(@RequestBody TaskStaticDTO dto) {
        try {
            if(!SecurityUtils.isAdmin()){
                dto.setCustomerId(SecurityUtils.getUserId());
            }
            dto.setStatusList(Collections.singletonList("2"));
            return Result.ok(taskService.successTaskStatic(dto));
        }catch (Exception e){
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "进行中任务统计导出", httpMethod = "POST", notes = "进行中任务统计导出")
    @PostMapping(value = "/manage/open/task/export/process_task_static")
    public void exportProcessTaskStatic(@RequestBody TaskStaticExportDTO dto, HttpServletResponse response) {
        TaskStaticDTO staticDTO = new TaskStaticDTO();
        staticDTO.setStartTime(dto.getStartTime());
        staticDTO.setEndTime(dto.getEndTime());
        if(!SecurityUtils.isAdmin()){
            staticDTO.setCustomerId(SecurityUtils.getUserId());
        }
        staticDTO.setStatusList(Collections.singletonList("2"));
        List<SuccessTaskStaticVo> successTaskStaticVos = taskService.successTaskStatic(staticDTO);
        ExportUtils.HttpExport(response,SuccessTaskStaticVo.class,successTaskStaticVos,"进行中任务统计","进行中任务统计");
    }

    @ApiOperation(value = "等待中任务统计导出", httpMethod = "POST", notes = "等待中任务统计导出")
    @PostMapping(value = "/manage/open/task/export/waiting_task_static")
    public void exportWaitingTaskStatic(@RequestBody TaskStaticExportDTO dto, HttpServletResponse response) {
        TaskStaticDTO staticDTO = new TaskStaticDTO();
        staticDTO.setStartTime(dto.getStartTime());
        staticDTO.setEndTime(dto.getEndTime());
        if(!SecurityUtils.isAdmin()){
            staticDTO.setCustomerId(SecurityUtils.getUserId());
        }
        staticDTO.setStatusList(Collections.singletonList("1"));
        List<SuccessTaskStaticVo> successTaskStaticVos = taskService.successTaskStatic(staticDTO);
        ExportUtils.HttpExport(response,SuccessTaskStaticVo.class,successTaskStaticVos,"等待中任务统计","等待中任务统计");
    }

    @ApiOperation(value = "失败任务统计", httpMethod = "POST", notes = "任务统计")
    @PostMapping(value = "/manage/open/task/fail_task_static")
    public Result<List<SuccessTaskStaticVo>> failTaskStatic(@RequestBody TaskStaticDTO dto) {
        try {
            if(!SecurityUtils.isAdmin()){
                dto.setCustomerId(SecurityUtils.getUserId());
            }
            dto.setStatusList(Collections.singletonList("2"));
            return Result.ok(taskService.failTaskStatic(dto));
        }catch (Exception e){
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "失败设备数量明细", httpMethod = "POST", notes = "失败设备数量明细")
    @PostMapping(value = "/manage/open/task/fail_padCode_list")
    public Result<Page<FailPadDetailVo>> failPadCodeList(@RequestBody TaskStaticDTO dto) {
        try {
            if(!SecurityUtils.isAdmin()){
                dto.setCustomerId(SecurityUtils.getUserId());
            }
            PageHelper.startPage(dto.getPage(), dto.getRows());
            return Result.ok( new Page<>(taskService.failPadCodeList(dto)));
        }catch (Exception e){
            return Result.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "失败设备数量明细导出", httpMethod = "POST", notes = "失败设备数量明细导出")
    @PostMapping(value = "/manage/open/task/export/fail_padCode_list")
    public void exportFailPadCodeList(@RequestBody TaskStaticDTO dto,HttpServletResponse response) {
            if(!SecurityUtils.isAdmin()){
                dto.setCustomerId(SecurityUtils.getUserId());
            }
            List<FailPadDetailVo> list = taskService.failPadCodeList(dto);
            ExportUtils.HttpExport(response,FailPadDetailVo.class,list,"失败设备数量明细导出","失败设备数量明细导出");

    }


    @ApiOperation(value = "失败任务统计导出", httpMethod = "POST", notes = "失败任务统计导出")
    @PostMapping(value = "/manage/open/task/export/fail_task_static")
    public void exportFailTaskStatic(@RequestBody TaskStaticExportDTO dto, HttpServletResponse response) {
        TaskStaticDTO staticDTO = new TaskStaticDTO();
        staticDTO.setStartTime(dto.getStartTime());
        staticDTO.setEndTime(dto.getEndTime());
        if(!SecurityUtils.isAdmin()){
            staticDTO.setCustomerId(SecurityUtils.getUserId());
        }
        List<SuccessTaskStaticVo> successTaskStaticVos = taskService.failTaskStatic(staticDTO);
        ExportUtils.HttpExport(response,SuccessTaskStaticVo.class,successTaskStaticVos,"失败任务统计导出","失败任务统计导出");
    }


    @ApiOperation(value = "备份任务", httpMethod = "POST", notes = "备份任务")
    @RequestMapping(value = "/manage/open/task/backupTask")
    public Result<Page<TaskBackupVO>> backupTask(@RequestBody TaskBackupDTO param) {
        Page<TaskBackupVO> taskVOPage = taskService.listBackupTasks(param);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "还原任务", httpMethod = "POST", notes = "还原任务")
    @RequestMapping(value = "/manage/open/task/restoreTask")
    public Result<Page<TaskRestoreVO>> restoreTask(@RequestBody TaskRestoreDTO param) {
        Page<TaskRestoreVO> taskVOPage = taskService.listRestoreTasks(param);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "获取备份列表", httpMethod = "GET", notes = "获取备份列表")
    @GetMapping(value = "/manage/open/task/listCustomerBackups")
    public Result<List<CustomerBackupVO>> listCustomerBackups(@Validated ListCustomerBackupDTO dto) {
        return Result.ok(taskService.listCustomerBackups(dto));
    }

    @ApiOperation(value = "删除备份", httpMethod = "POST", notes = "删除备份")
    @RequestMapping(value = "/manage/open/task/deleteBackup")
    public Result<?> deleteBackup(@RequestBody List<Long> backupIds) {
        taskService.deleteBackups(backupIds);
        return Result.ok();
    }

    @ApiOperation(value = "任务列表", httpMethod = "POST", notes = "任务列表")
    @RequestMapping(value = "/manage/open/task/list")
    public Result<Page<AllTaskVO>> list(@RequestBody TaskDTO param) {
        TaskDTO taskDTO = taskService.taskCustomerSearch(param);
        if(null==taskDTO){
            return Result.ok(new Page<AllTaskVO>());
        }
        Page<AllTaskVO> taskVOPage = taskService.allListTasks(taskDTO);
        return Result.ok(taskVOPage);
    }

    @ApiOperation(value = "所有任务类型", httpMethod = "POST", notes = "所有任务类型")
    @RequestMapping(value = "/manage/open/taskType/list")
    public Result<List<AllTaskTypeVO>> taskTypeList(@RequestBody TaskDTO param) {
        List<AllTaskTypeVO> allTaskTypeVOList = new ArrayList<>();
        List<TaskTypeAndChannelEnum> taskTypeAndChannelEnumList = TaskTypeAndChannelEnum.showList(param.getTaskSelectType());
        taskTypeAndChannelEnumList.forEach(taskTypeAndChannelEnum -> {
            AllTaskTypeVO allTaskTypeVO = new AllTaskTypeVO();
            allTaskTypeVO.setTaskType(taskTypeAndChannelEnum.getTaskCode());
            allTaskTypeVO.setName(taskTypeAndChannelEnum.getTaskTypeName());
            allTaskTypeVO.setTaskSelectType(taskTypeAndChannelEnum.getTaskSelectType());
            allTaskTypeVOList.add(allTaskTypeVO);
        });
        return Result.ok(allTaskTypeVOList);
    }
}
