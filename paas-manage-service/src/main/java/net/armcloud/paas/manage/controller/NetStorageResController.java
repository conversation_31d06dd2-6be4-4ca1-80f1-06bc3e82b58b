package net.armcloud.paas.manage.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.model.dto.NetStorageResDTO;
import net.armcloud.paas.manage.model.dto.NetStorageResDetailDTO;
import net.armcloud.paas.manage.model.dto.NetWorkVirtualizeManageDTO;
import net.armcloud.paas.manage.model.vo.NetStorageResListVo;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paas.manage.model.vo.StorageCapacityDetailVO;
import net.armcloud.paas.manage.service.INetStorageResService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import net.armcloud.paas.manage.model.vo.NetStorageResVo;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * 网存资源操作
 */
@RestController
@RequestMapping("/manage/net/storage/res")
@Api(tags = "网存资源")
public class NetStorageResController {

    @Autowired
    private INetStorageResService netStorageResService;

    @PostMapping(value = "/add")
    @ApiOperation(value = "添加网络存储资源", httpMethod = "POST", notes = "添加网络存储资源")
    public  Result<?> add(@Valid @RequestBody NetStorageResDTO param) {
        if(!SecurityUtils.isAdmin()){
            throw new BasicException(100002,"非管理员无权操作");
        }
        param.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        return Result.ok(netStorageResService.save(param));
    }

    @PostMapping(value = "/unsubscribe")
    @ApiOperation(value = "退订网络存储资源", httpMethod = "POST", notes = "退订网络存储资源")
    public Result<?> unsubscribe(@Valid @RequestBody NetStorageResDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        return Result.ok(netStorageResService.unsubscribe(param));
    }


    @PostMapping(value = "/detail/byCustomerId")
    @ApiOperation(value = "根据用户id获取网存资源详情", httpMethod = "POST", notes = "根据用户id获取网存资源详情")
    public Result<NetStorageResVo> getDetailByCustomerId(@Valid @RequestBody NetStorageResDetailDTO param) {
        if(Objects.isNull(param.getCustomerId()) && !SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return Result.ok(netStorageResService.getDetailByCustomerId(param));
    }

    /**
     * 用户界面查看网存资源
     * @param param
     * @return
     */

    @PostMapping(value = "/detail/user/byCustomerId")
    @ApiOperation(value = "根据用户id获取网存资源详情", httpMethod = "POST", notes = "根据用户id获取网存资源详情")
    public Result<NetStorageResVo> userGetDetailByCustomerId(@Valid @RequestBody NetStorageResDetailDTO param) {
        if(Objects.isNull(param.getCustomerId()) && !SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return Result.ok(netStorageResService.userGetDetailByCustomerId(param));
    }


    @PostMapping(value = "/detail/list")
    @ApiOperation(value = "获取网存详情列表(管理员)", httpMethod = "POST", notes = "获取网存详情列表")
    public Result<Page<NetStorageResListVo>> getDetailList(@Valid @RequestBody NetStorageResDetailDTO param) {
//        if(Objects.isNull(param.getCustomerId())){
//            param.setCustomerId(SecurityUtils.getUserId());
//        }
        return Result.ok(netStorageResService.getDetailList(param));
    }


    @PostMapping(value = "/detail/list/padCode")
    @ApiOperation(value = "获取网存详情列表(用户)", httpMethod = "POST", notes = "获取网存详情列表")
    public Result<Page<PadVO>> getPadCodeDetailList(@RequestBody NetStorageResDetailDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        return Result.ok(netStorageResService.getPadCodeDetailList(param));
    }

    @PostMapping(value = "/detail/storageCapacity/available")
    @ApiOperation(value = "可用网存资源大小", httpMethod = "POST", notes = "根据用户id获取网存剩余资源详情（前端校验用）")
    public Result<StorageCapacityDetailVO> getDetailStorageCapacityAvailable(@Valid @RequestBody NetStorageResDetailDTO param) {
        if(Objects.isNull(param.getCustomerId())){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return Result.ok(netStorageResService.getDetailStorageCapacityAvailable(param));
    }


    @RequestMapping(value = "/virtualize", method = RequestMethod.POST)
    @ApiOperation(value = "创建网存云机", httpMethod = "POST", notes = "创建云机")
    public Result<String> virtualizeDevice(@RequestBody NetWorkVirtualizeManageDTO param) {

        NetStorageResDetailDTO dto = new NetStorageResDetailDTO();

        dto.setClusterCode(param.getClusterCode());
        if(Objects.nonNull(param.getCustomerId())&&!SecurityUtils.isAdmin()){
            dto.setGetTheCurrentUserFlag(true);
        }
        if(Objects.isNull(param.getCustomerId())&&!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        dto.setCustomerId(param.getCustomerId());
        StorageCapacityDetailVO detailStorageCapacityAvailable = netStorageResService.getDetailStorageCapacityAvailable(dto);
        //可使用总数量
        Long storageCapacityAvailable = detailStorageCapacityAvailable.getStorageCapacityAvailable();
        long useTheSize = (long)param.getStorageSize() * param.getNumber();
        if(useTheSize>storageCapacityAvailable){
            throw new BasicException(
                    String.format("网存空间不足以创建当前数量实例 网存剩余可用空间 %s (GB), 当前需要空间 %s (GB)",
                            storageCapacityAvailable, useTheSize)
            );
        }
        return Result.ok(netStorageResService.virtualizeDevice(param));
    }


    @RequestMapping(value = "/dict", method = RequestMethod.GET)
    @ApiOperation(value = "网存大小枚举list", httpMethod = "GET", notes = "网存大小枚举list")
    public Result<List<Integer>> netStorageDict() {
        List<Integer> list = new ArrayList<>(Arrays.asList(4, 16, 32, 64, 128, 256));
        return Result.ok(list);
    }
}
