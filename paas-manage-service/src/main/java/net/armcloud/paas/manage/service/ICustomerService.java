package net.armcloud.paas.manage.service;

import net.armcloud.paas.manage.model.dto.CustomerDTO;
import net.armcloud.paas.manage.model.vo.CustomerSelectionVO;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;

import java.util.List;

public interface ICustomerService{

    /**
     * 删除
     * @param id
     * @return
     */
    void deleteByPrimaryKey(Long id);

    /**
     * 新增
     * @param record
     * @return
     */
    Result<CustomerVO> insert(CustomerDTO record);

    /**
     * 修改
     * @param record
     * @return
     */
    Result<?> updateByPrimaryKey(CustomerDTO record);

    /**
     * 分页查询
     * @param customerDto
     * @return
     */
    Page<CustomerVO> selectPageList(CustomerDTO customerDto);

    /**
     * 查询账户信息
     * @param id
     * @return
     */
    CustomerVO selectByPrimaryKey(Long id);

    // 批量查询
    List<CustomerVO> selectBatchByIds(List<Long> ids);

    List<Long> selectByIdAndAccount(CustomerDTO customerDTO);

    /**
     * 根据用户名密码查询
     * @param username
     * @param password
     */
    CustomerVO selectByNameAndPassword(String username, String password);

    /**
     * 重置密码
     * @param id
     * @return
     */
    String resetPassword(Long id);

    /**
     * 启用用户
     * @param id
     */
    void enableCustomer(Long id);

    /**
     * 查询所有客户
     * @return
     */
    List<CustomerVO> selectionListCustomer(CustomerDTO param);

    void addAdminInfo(String username, String password);

    List<CustomerSelectionVO> getCustomerSelection();
}
