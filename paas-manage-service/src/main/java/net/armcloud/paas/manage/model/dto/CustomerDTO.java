package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class CustomerDTO extends PageDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    //校验只能为汉字
    private String customerName;

    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String associates;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    //校验不为中文
    @Pattern(regexp = "^[^\\u4e00-\\u9fa5]*$", message = "客户账号不能包含中文字符")
    private String customerAccount;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private Long customerTel;

    @ApiModelProperty(value = "状态 0-禁用；1-启用。默认为1")
    private Integer status;

    /**
     * 是否内部用户（1-是 0-否）
     */
    @ApiModelProperty(value = "是否内部用户（1-是 0-否）", required = true)
    @NotNull(message = "是否内部用户不能为空")
    private Integer isInternal;

    /**
     * 查询
     */
    @ApiModelProperty(value = "查询")
    private String search;

    /**
     * 角色id
     */
    private Long[] roleIds;
}
