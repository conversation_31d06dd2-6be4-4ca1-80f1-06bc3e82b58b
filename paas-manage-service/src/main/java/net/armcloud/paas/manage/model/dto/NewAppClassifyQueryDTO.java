package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 应用列表查询对象
 */
@Data
public class NewAppClassifyQueryDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "应用分类名称")
    private String classifyName;

    @ApiModelProperty(value = "分类状态 true启用 false禁用")
    private Boolean enable;
}
