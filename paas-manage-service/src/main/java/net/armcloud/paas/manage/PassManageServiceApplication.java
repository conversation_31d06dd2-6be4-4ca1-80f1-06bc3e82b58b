package net.armcloud.paas.manage;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableFeignClients(basePackages = {
        "net.armcloud.paas.manage.client.internal.stub",
        "net.armcloud.paas.manage.client.internal.feign",
        "net.armcloud.paas.manage.client.internal.facade"})
@MapperScan(
        basePackages =
                {
                        "net.armcloud.paas.manage.mapper",
                        "net.armcloud.paas.manage.authorization.mapper",
                        "net.armcloud.paas.manage.customerlabel.mapper"
                },
        sqlSessionTemplateRef = "sqlSessionTemplate",
        annotationClass = Mapper.class)
public class PassManageServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(PassManageServiceApplication.class, args);
    }

}