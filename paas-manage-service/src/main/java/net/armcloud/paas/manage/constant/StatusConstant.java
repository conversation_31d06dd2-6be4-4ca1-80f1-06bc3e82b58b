package net.armcloud.paas.manage.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static net.armcloud.paas.manage.constant.TaskTypeConstants.*;

public class StatusConstant {
    /**
     * 已删除
     */
    public static final Integer DELETED = 1;

    /**
     * 未删除
     */
    public static final Integer NOT_DELETED = 0;

    /**
     * 已分配
     */
    public static final Integer ALLOCATED = 1;
    /**
     * 已分配
     */
    public static final String ALLOCATED_NAME = "已分配";

    /**
     * 未分配
     */
    public static final Integer NOT_ALLOCATED = 0;
    /**
     * 未分配
     */
    public static final String NOT_ALLOCATED_NAME = "未分配";

    /**
     * 文件类型- 1 文件
     */
    public static final Integer FILE_TYPE_FILE = 1;

    /**
     * 文件类型- 2 应用
     */
    public static final Integer FILE_TYPE_APP = 2;

//    /**
//     * 任务类型 1000：实例重启
//     */
//    public static final Integer TASK_TYPE_RESTART = 1000;
//
//    /**
//     * 任务类型 1001：实例重置
//     */
//    public static final Integer TASK_TYPE_RESET = 1001;
//
//    /**
//     * 任务类型 1002：pad执行命令
//     */
//    public static final Integer TASK_TYPE_POD_EXEC = 1002;
//
//    /**
//     * 任务类型 1003：下载应用
//     */
//    public static final Integer TASK_TYPE_DOWNLOAD_APP = 1003;
//
//    /**
//     * 任务类型 1004：应用卸载
//     */
//    public static final Integer TASK_TYPE_UNINSTALL_APP = 1004;
//
//    /**
//     * 任务类型 1005：应用停止
//     */
//    public static final Integer TASK_TYPE_STOP_APP = 1005;
//
//    /**
//     * 任务类型 1006：应用重启
//     */
//    public static final Integer TASK_TYPE_RESTART_APP = 1006;
//
//    /**
//     * 任务类型 1007：应用启动
//     */
//    public static final Integer TASK_TYPE_START_APP = 1007;
//
//    /**
//     * 任务类型 1008：本地截图
//     */
//    public static final Integer TASK_TYPE_LOCAL_SCREENSHOT = 1008;
//
//    /**
//     * 任务类型 1009：下载文件
//     */
//    public static final Integer TASK_TYPE_DOWNLOAD_FILE = 1009;
//
//    /**
//     * 任务类型 1010：修改实例属性
//     */
//    public static final Integer TASK_TYPE_UPDATE_POD = 1010;
//
//    /**
//     * 任务类型 1011：查询已安装应用
//     */
//    public static final Integer TASK_TYPE_QUERY_INSTALLED_APP = 1011;
//
//    /**
//     * 任务类型 1012：升级镜像
//     */
//    public static final Integer TASK_TYPE_UPGRADE_IMAGE = 1012;
//
//    /**
//     * 任务类型 1013：应用安装
//     */
//    public static final Integer TASK_TYPE_INSTALL_APP = 1013;
//
//    /**
//     * 任务类型 1014：应用清理
//     */
//    public static final Integer TASK_TYPE_CLEAN_APP = 1014;
//
//    /**
//     * 任务类型 2000：上传文件
//     */
//    public static final Integer TASK_TYPE_UPLOAD_FILE = 2000;
//
//    /**
//     * 任务类型 2001：删除文件
//     */
//    public static final Integer TASK_TYPE_DELETE_FILE = 2001;

    /**
     * 任务类型 实例任务类型
     */
    public static final List<Integer> POD_TASK_TYPES = new ArrayList<>(Arrays.asList(
            RESTART.getType(),
            RESET.getType(),
            EXECUTE_COMMAND.getType(),
            SCREENSHOT_LOCAL.getType(),
            UPDATE_PAD_PROPERTIES.getType(),
            UPGRADE_IMAGE.getType(),
            UPDATE_PAD_PROPERTIES.getType(),
            PAD_SET_NETWORK_PROXY.getType(),
            GET_PAD_NETWORK_PROXY_INFO.getType(),
            CHANGE_LANGUAGE.getType(),
            CHANGE_TIME_ZONE.getType(),
            UPDATE_SIM.getType(),
            LIMIT_BANDWIDTH.getType(),
            CONTAINER_NET_STORAGE_OFF.getType(),
            CONTAINER_NET_STORAGE_ON.getType(),
            UPDATE_PAD_ANDROID_PROP.getType(),
            GPS_INJECT_INFO.getType(),
            REPLACE_PAD.getType(),
            MODIFY_PROPERTIES_PAD.getType(),
            VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(),
            REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(),
            REPLACE_REAL_ADB.getType(),
            CLEAR_APP_HOME.getType(),
            UPDATE_CONTACTS.getType(),
            CONTAINER_NET_STORAGE_ON.getType(),
            CONTAINER_NET_STORAGE_OFF.getType(),
            CONTAINER_NET_STORAGE_DELETE.getType(),
            CONTAINER_NET_STORAGE_BACKUP.getType(),
            CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType(),
            SET_WIFI_LIST.getType(),
            SIMULATE_TOUCH.getType(),
            ADD_PHONE_RECORD.getType(),
            SET_COMMIT_TEXT.getType(),
            RESET_GAID.getType()
    ));

    /**
     * 任务类型 应用任务类型
     */
    public static final List<Integer> APP_TASK_TYPES = new ArrayList<>(Arrays.asList(
            DOWNLOAD_APP.getType(),
            UNINSTALL_APP.getType(),
            STOP_APP.getType(),
            START_APP.getType(),
            RESTART_APP.getType(),
            CLEAN_APP.getType(),
            APP_BLACK_LIST.getType(),
            LIST_INSTALLED_APP.getType(),
            APP_WHITE_LIST.getType()
    ));

    /**
     * 任务类型 文件上传任务类型
     */
    public static final List<Integer> FILE_TASK_TYPES =new ArrayList<>(Arrays.asList(DOWNLOAD_FILE.getType()));
    public static final List<Integer> DEVICE_TASK_TYPES =new ArrayList<>(Arrays.asList(DEVICE_RESTART.getType(),POWER_RESET.getType(),CONTAINER_VIRTUALIZE.getType(),CONTAINER_DEVICE_DESTROY.getType(),SET_GATEWAY.getType(),CBS_SELF_UPDATE.getType(),CREATE_DEVICE.getType(),CREATE_DEVICE_SELF_INSPECTION.getType(),BRUSH_CORE_ARM.getType()));

    /**
     * 同步状态 待同步
     */
    public static final String SYNC_STATUS_WAIT_NAME = "待同步";

    /**
     * 同步状态 已同步
     */
    public static final String SYNC_STATUS_SYNCED_NAME = "已同步";

    /**
     * 同步状态 同步失败
     */
    public static final String SYNC_STATUS_FAILED_NAME = "同步失败";

    /**
     * 同步状态 部分同步
     */
    public static final String SYNC_STATUS_PARTIAL_NAME = "部分同步";

    /**
     * 客户状态-0 禁用
     */
    public static final Integer CUSTOMER_STATUS_DISABLE = 0;

    /**
     * 客户状态-1 启用
     */
    public static final Integer CUSTOMER_STATUS_ENABLE = 1;

    public static final String MANAGE = "manage_";
    public static final String MANAGE_UUID = "manage_uuid_";

    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";
}
