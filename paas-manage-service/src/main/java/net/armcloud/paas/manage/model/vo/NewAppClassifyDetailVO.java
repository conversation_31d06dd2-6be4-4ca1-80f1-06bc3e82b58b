package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 应用分类详情响应对象
 */
@Data
public class NewAppClassifyDetailVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "用户id")
    private Long customerId;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;
    @ApiModelProperty(value = "应用数量")
    private Integer appNum;
    @ApiModelProperty(value = "分类状态 是否启用(1：是；0：否) 默认1")
    private Boolean enable;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "应用列表")
    private List<AppInfo> appInfos;

    @Data
    public static class AppInfo{
        @ApiModelProperty(value = "文件id")
        private Long fileId;
        @ApiModelProperty(value = "应用id")
        private Long appId;
    }
}
