package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
public class CustomerVO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String associates;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private Long customerTel;

    /**
     * 实例数量
     */
    @ApiModelProperty(value = "实例数量")
    private Long instanceCount;

    /**
     * 状态 0-禁用；1-启用
     */
    @ApiModelProperty(value = "状态 0-禁用；1-启用")
    private Integer status;
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 密钥key
     */
    @ApiModelProperty(value = "密钥key")
    private String accessKeyId;

    /**
     * 密钥secret
     */
    @ApiModelProperty(value = "密钥secret")
    private String secretAccessKey;


    /**
     * 用户拥有的角色
     */
    @ApiModelProperty(value = "用户拥有的角色")
    private List<String> roles;


    /**
     * 用户拥有的角色
     */
    @ApiModelProperty(value = "查询的角色")
    private List<Long> roleIds;
    private String roleNames;  // 直接映射 SQL 的 `role_names`

    /**
     * 是否内部用户（1-是 0-否）
     */
    @ApiModelProperty(value = "是否内部用户（1-是 0-否）")
    private Integer isInternal;

    public String getRoleNames() {
        return roleNames;
    }

    public void setRoleNames(String roleNames) {
        this.roleNames = roleNames;

        // 如果 `roleNames` 不为空，则自动拆分为 List
        if (roleNames != null && !roleNames.isEmpty()) {
            this.roles = Arrays.asList(roleNames.split(","));
        }
    }

}
