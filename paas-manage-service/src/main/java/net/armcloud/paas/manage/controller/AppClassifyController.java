package net.armcloud.paas.manage.controller;

import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.AppClassifyPadSaveDTO;
import net.armcloud.paas.manage.model.dto.AppClassifyQueryDTO;
import net.armcloud.paas.manage.model.dto.AppClassifySaveDTO;
import net.armcloud.paas.manage.model.vo.AppClassifyDetailVO;
import net.armcloud.paas.manage.model.vo.AppClassifyPadDetailVO;
import net.armcloud.paas.manage.model.vo.AppClassifyVO;
import net.armcloud.paas.manage.service.IAppClassifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/manage/appClassify")
@Api(tags = "黑白名单管理")
public class AppClassifyController {
    @Resource
    private IAppClassifyService appClassifyService;

    @RequestMapping(value = "/pageList", method = RequestMethod.GET)
    @ApiOperation(value = "黑白名单列表", httpMethod = "GET", notes = "黑白名单列表")
    public Result<Page<AppClassifyVO>> list(AppClassifyQueryDTO param) {
        return Result.ok(appClassifyService.list(param));
    }

    @RequestMapping(value = "/simpleList", method = RequestMethod.GET)
    @ApiOperation(value = "黑白名单简单列表", httpMethod = "GET", notes = "黑白名单简单列表")
    public Result<List<AppClassifyVO>> simplelist(@RequestParam("customerId")Long customerId) {
        return Result.ok(appClassifyService.simpleList(customerId));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "黑白名单详情", httpMethod = "GET", notes = "黑白名单详情")
    public Result<AppClassifyDetailVO> detail(@RequestParam("id") Long id) {
        return Result.ok(appClassifyService.detail(id));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单保存", httpMethod = "POST", notes = "黑白名单保存")
    public Result<?> save(@Valid @RequestBody AppClassifySaveDTO param) {
        appClassifyService.save(param);
        return Result.ok();
    }

    @RequestMapping(value = "/padDetail", method = RequestMethod.GET)
    @ApiOperation(value = "黑白名单实例关联详情", httpMethod = "GET", notes = "黑白名单实例关联详情")
    public Result<AppClassifyPadDetailVO> padDetail(@RequestParam("id") Long id) {
        return Result.ok(appClassifyService.padDetail(id));
    }

    @RequestMapping(value = "/padSave", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单实例关联保存", httpMethod = "POST", notes = "黑白名单实例关联保存")
    public Result<?> padSave(@Valid @RequestBody AppClassifyPadSaveDTO param) {
        appClassifyService.padSave(param);
        return Result.ok();
    }

    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ApiOperation(value = "删除黑白名单", httpMethod = "POST", notes = "删除黑白名单")
    public Result<?> del(@RequestParam("id") Long id) {
        appClassifyService.del(id);
        return Result.ok();
    }

    @RequestMapping(value = "/appBlackExcelTemplate")
    @ApiOperation(value = "应用黑白名单excel模板下载", notes = "应用黑白名单excel模板下载")
    public ResponseEntity<InputStreamResource> appBlackExcelTemplate() throws IOException {
        // 加载类路径下的Excel文件
        ClassPathResource excelFile = new ClassPathResource("excel/appBlackTemplate.xlsx");

        // 设置HTTP响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=appBlackTemplate.xlsx");
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel");

        // 创建InputStreamResource来包装文件输入流
        InputStreamResource resource = new InputStreamResource(excelFile.getInputStream());

        // 构建并返回ResponseEntity
        return ResponseEntity.ok().headers(headers).body(resource);
    }
}
