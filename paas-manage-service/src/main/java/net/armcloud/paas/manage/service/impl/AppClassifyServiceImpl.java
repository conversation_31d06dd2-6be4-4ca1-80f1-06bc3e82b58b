package net.armcloud.paas.manage.service.impl;

import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.facade.PadInternalFacade;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.mapper.paas.CustomerAppClassifyMapper;
import net.armcloud.paas.manage.mapper.paas.CustomerAppClassifyPadRelationMapper;
import net.armcloud.paas.manage.mapper.paas.CustomerAppClassifyRelationMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.model.dto.AppClassifyPadSaveDTO;
import net.armcloud.paas.manage.model.dto.AppClassifyQueryDTO;
import net.armcloud.paas.manage.model.dto.AppClassifySaveDTO;
import net.armcloud.paas.manage.model.vo.AppClassifyDetailVO;
import net.armcloud.paas.manage.model.vo.AppClassifyPadDetailVO;
import net.armcloud.paas.manage.model.vo.AppClassifyVO;
import net.armcloud.paas.manage.model.vo.PadInfoVO;
import net.armcloud.paas.manage.service.IAppClassifyService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.TriggeringBlackDTO;
import net.armcloud.paascenter.common.model.dto.console.vo.ConsolePadFileVO;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.entity.paas.Customer;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyPadRelation;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyRelation;
import net.armcloud.paas.manage.mapper.paas.UserFileMapper;
import net.armcloud.paas.manage.mapper.paas.CustomerMapper;

/**
 * 黑白名单业务层
 */
@Service
@Slf4j
public class AppClassifyServiceImpl implements IAppClassifyService {
    @Resource
    private CustomerAppClassifyMapper customerAppClassifyMapper;
    @Resource
    private CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper;
    @Resource
    private CustomerAppClassifyPadRelationMapper customerAppClassifyPadRelationMapper;
    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private PadInternalFacade padInternalFacade;
    @Resource
    private PadMapper padMapper;
    @Resource
    private UserFileMapper userFileMapper;

    /**
     * 设置黑白名单线程池
     */
    private final ExecutorService whiteAndBlackThreadPool = new ThreadPoolExecutor(5, 10,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000), new ThreadFactoryBuilder()
                    .setNameFormat("SendNewAppWhiteAndBlackListThreadPool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 分页获取黑白名单列表
     *
     * @param param
     * @return
     */
    @Override
    public Page<AppClassifyVO> list(AppClassifyQueryDTO param) {
        if (!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        PageHelper.startPage(param.getPage(), param.getRows());
        List<CustomerAppClassify> appMarketVOList = customerAppClassifyMapper
                .selectList(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("delete_flag", 0)
                        .eq(param.getCustomerId() != null, "customer_id", param.getCustomerId())
                        .eq(param.getClassifyType() != null, "classify_type", param.getClassifyType())
                        .like(StrUtil.isNotEmpty(param.getClassifyName()), "classify_name",
                                "%" + param.getClassifyName() + "%"));
        for (CustomerAppClassify customerAppClassify : appMarketVOList) {
            customerAppClassify.setAppNum(getAppNum(customerAppClassify.getId()));
        }
        List<AppClassifyVO> appClassifyVOList = BeanUtil.copyToList(appMarketVOList, AppClassifyVO.class);
        Page page = new Page<>(appMarketVOList);
        // 获取用户名称
        if (CollUtil.isNotEmpty(appClassifyVOList)) {
            Set<Long> customerIds = appClassifyVOList.stream().map(AppClassifyVO::getCustomerId)
                    .collect(Collectors.toSet());
            List<Customer> customers = customerMapper.selectByIds(new ArrayList<>(customerIds));
            Map<Long, String> customerMap = customers.stream()
                    .collect(Collectors.toMap(Customer::getId, Customer::getCustomerName, (key1, key2) -> key1));
            for (AppClassifyVO appClassifyVO : appClassifyVOList) {
                appClassifyVO.setCustomerName(customerMap.get(appClassifyVO.getCustomerId()));
            }
        }
        page.setPageData(appClassifyVOList);
        return page;
    }

    private int getAppNum(Long id) {
        Long count = customerAppClassifyRelationMapper.selectCount(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                .eq("app_classify_id", id));
        return count != null ? count.intValue() : 0;
    }

    /**
     * 根据客户id获取黑白名单
     *
     * @param customerId
     * @return
     */
    @Override
    public List<AppClassifyVO> simpleList(Long customerId) {
        List<CustomerAppClassify> appMarketVOList = customerAppClassifyMapper
                .selectList(new QueryWrapper<>(CustomerAppClassify.class)
                        .select("id", "classify_name")
                        .eq("delete_flag", 0)
                        .eq("customer_id", customerId));
        for (CustomerAppClassify customerAppClassify : appMarketVOList) {
            customerAppClassify.setAppNum(getAppNum(customerAppClassify.getId()));
        }
        List<AppClassifyVO> appClassifyVOList = BeanUtil.copyToList(appMarketVOList, AppClassifyVO.class);
        return appClassifyVOList;
    }

    /**
     * 获取黑白名单详情
     *
     * @param id
     * @return
     */
    @Override
    public AppClassifyDetailVO detail(Long id) {
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper.selectById(id);
        if (customerAppClassify == null || customerAppClassify.getDeleteFlag()) {
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        Customer customer = customerMapper.selectById(customerAppClassify.getCustomerId());
        if (customer == null) {
            throw new BasicException(USER_DOES_NOT_EXIST);
        }
        List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper
                .selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                        .eq("app_classify_id", customerAppClassify.getId())
                        .eq("customer_id", customerAppClassify.getCustomerId()));

        // 计算黑白名单数量
        customerAppClassify.setAppNum(customerAppClassifyRelationList.size());
        AppClassifyDetailVO appClassifyDetailVO = buildAppClassifyDetailVO(customerAppClassify,
                customerAppClassifyRelationList, customer);
        return appClassifyDetailVO;
    }

    /**
     * 保存编辑黑白名单
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AppClassifySaveDTO param) {
        // 非管理员 需要校验客户id
        if (!checkCustomerIdLegitimacy(param.getCustomerId())) {
            throw new BasicException(CUSTOMER_ID_ILLEGALITY);
        }
        Boolean isInsert = param.getId() == null;
        CustomerAppClassify appClassifyStatus = null;
        if (!isInsert) {
            // 编辑
            // 判断该客户应用市场配置是否正常
            appClassifyStatus = customerAppClassifyMapper.selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                    .eq("id", param.getId()).eq("delete_flag", 0));
            if (appClassifyStatus == null) {
                throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
            }
        } else {
            // 新增时 关联所有实例的模式下 一个类型只能存一条数据
            if (param.getApplyAllInstances() == null || param.getApplyAllInstances()) {
                boolean exists = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("customer_id", param.getCustomerId()).eq("classify_type", param.getClassifyType())
                        .eq("delete_flag", 0));
                if (exists) {
                    throw new BasicException(CUSTOMER_APP_CLASSIF_LIMIT_ONE);
                }
            } else {
                // 同一个类型不能同时存在两个模式(由于模式1只限制一条 所以不用额外校验)
                boolean exists = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("customer_id", param.getCustomerId()).eq("classify_type", param.getClassifyType())
                        .eq("apply_all_instances", 1).eq("delete_flag", 0));
                if (exists) {
                    throw new BasicException(CUSTOMER_APP_CLASSIF_LIMIT_MODE);
                }
            }
        }

        // 判断分类名在该客户下是否重复
        Boolean classifyNameExist = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("delete_flag", 0)
                .eq("customer_id", param.getCustomerId())
                .eq("classify_type", param.getClassifyType())
                .eq("classify_name", param.getClassifyName())
                .notIn(!isInsert, "id", param.getId()));
        if (classifyNameExist) {
            throw new BasicException(CUSTOMER_APP_CLASSIF_NAME_REPEAT);
        }
        // 构建黑白名单对象
        CustomerAppClassify customerAppClassifySave = buildCustomerAppClassify(isInsert, param);
        if (isInsert) {
            customerAppClassifyMapper.cusInsert(customerAppClassifySave);
        } else {
            // 清除该用户所有关联分类应用
            customerAppClassifyRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                    .eq("app_classify_id", param.getId()).eq("customer_id", param.getCustomerId()));
            customerAppClassifyMapper.updateById(customerAppClassifySave);
        }

        if (CollUtil.isNotEmpty(param.getAppInfos())) {
            // 构建黑白名单关联应用对象
            List<CustomerAppClassifyRelation> customerAppClassifyRelationSaveList = buildCustomerAppClassifyRelation(
                    param, customerAppClassifySave.getId());
            if (CollUtil.isNotEmpty(customerAppClassifyRelationSaveList)) {
                customerAppClassifyRelationMapper.batchInsert(customerAppClassifyRelationSaveList);
            }
        }

        boolean isBlack = param.getClassifyType() == 2;
        if (param.getApplyAllInstances() != null && !param.getApplyAllInstances()) {
            // 修改时发送黑白名单指令 新增时还未配置padCode则无需发送
            if (!isInsert) {
                // 先获取原先的padCode
                List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                        .selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                                .eq("customer_id", param.getCustomerId()).eq("app_classify_id", param.getId()));
                // 触发黑白名单
                sendAppWhiteAndBlackList(param.getCustomerId(), oldCustomerAppClassifyPadRelationList, isBlack);
            }
        } else {
            // 黑白名单 每次新增和编辑都要触发该客户下所有实例设置黑白名单
            List<PadInfoVO> pads = padMapper.selectValidPadCodeByCustomerId(param.getCustomerId());
            if (CollUtil.isNotEmpty(pads)) {
                sendNewAppWhiteAndBlackList(param.getCustomerId(), pads, isBlack);
            }
        }
    }

    /**
     * 黑白名单关联实例详情
     *
     * @param id
     * @return
     */
    @Override
    public AppClassifyPadDetailVO padDetail(Long id) {
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper.selectById(id);
        if (customerAppClassify == null || customerAppClassify.getDeleteFlag()) {
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                .selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                        .eq("app_classify_id", customerAppClassify.getId())
                        .eq("customer_id", customerAppClassify.getCustomerId()));
        AppClassifyPadDetailVO appClassifyDetailVO = buildAppClassifyPadDetailVO(customerAppClassify,
                customerAppClassifyPadRelationList);
        return appClassifyDetailVO;
    }

    /**
     * 黑白名单关联实例保存编辑
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void padSave(AppClassifyPadSaveDTO param) {
        // 非管理员 需要校验客户id
        if (!checkCustomerIdLegitimacy(param.getCustomerId())) {
            throw new BasicException(CUSTOMER_ID_ILLEGALITY);
        }
        // 判断该客户应用市场配置是否正常
        CustomerAppClassify appClassifyStatus = customerAppClassifyMapper
                .selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("id", param.getId()).eq("delete_flag", 0));
        if (appClassifyStatus == null) {
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }

        // 使用所有实例的模式不允许这里添加实例
        if (appClassifyStatus.getApplyAllInstances() != null && appClassifyStatus.getApplyAllInstances()) {
            throw new BasicException(NOW_MODE_NOT_ALLOW_ADD_PAD_CODE);
        }

        // 先获取原先的padCode
        List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                .selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                        .eq("customer_id", param.getCustomerId()).eq("app_classify_id", param.getId()));

        // 清除该用户分类下所有关联实例
        customerAppClassifyPadRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                .eq("customer_id", param.getCustomerId()).eq("app_classify_id", param.getId()));

        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationSaveList = null;
        if (CollUtil.isNotEmpty(param.getAppPadInfos())) {
            // 构建黑白名单关联应用对象
            customerAppClassifyPadRelationSaveList = buildCustomerAppClassifyPadRelation(param);
            if (CollUtil.isNotEmpty(customerAppClassifyPadRelationSaveList)) {
                customerAppClassifyPadRelationMapper.batchInsert(customerAppClassifyPadRelationSaveList);
                CustomerAppClassify customerAppClassifyUpdate = new CustomerAppClassify();
                customerAppClassifyUpdate.setId(param.getId());
                customerAppClassifyUpdate.setPadNum(customerAppClassifyPadRelationSaveList.size());
                customerAppClassifyUpdate.setUpdateBy(SecurityUtils.getUsername());
                customerAppClassifyMapper.updateById(customerAppClassifyUpdate);
            }
        }

        // 分离出新增的padCode和未新增的padCode
        List<CustomerAppClassifyPadRelation> addCustomerAppClassifyPadRelationList = new ArrayList<>();
        List<CustomerAppClassifyPadRelation> notAddCustomerAppClassifyPadRelationList = new ArrayList<>();
        Map<String, Long> newMap = null;
        if (CollUtil.isNotEmpty(customerAppClassifyPadRelationSaveList)) {
            addCustomerAppClassifyPadRelationList.addAll(customerAppClassifyPadRelationSaveList);
            newMap = customerAppClassifyPadRelationSaveList.stream()
                    .collect(Collectors.toMap(CustomerAppClassifyPadRelation::getPadCode,
                            CustomerAppClassifyPadRelation::getAppClassifyId, (key1, key2) -> key1));
        }
        if (CollUtil.isNotEmpty(oldCustomerAppClassifyPadRelationList)) {
            List<CustomerAppClassifyPadRelation> notAdd = new ArrayList<>();
            if (CollUtil.isNotEmpty(newMap)) {
                // 剥离出未新增的
                for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : oldCustomerAppClassifyPadRelationList) {
                    if (newMap.get(customerAppClassifyPadRelation.getPadCode()) == null) {
                        notAddCustomerAppClassifyPadRelationList.add(customerAppClassifyPadRelation);
                    }
                }
            } else {
                notAddCustomerAppClassifyPadRelationList.addAll(oldCustomerAppClassifyPadRelationList);
            }
        }
        // 发送黑白名单指令任务
        boolean isBlack = appClassifyStatus.getClassifyType() == 2;
        sendAppWhiteAndBlackList(param.getCustomerId(), addCustomerAppClassifyPadRelationList, isBlack);
        sendAppWhiteAndBlackList(param.getCustomerId(), notAddCustomerAppClassifyPadRelationList, isBlack);
    }

    /**
     * 删除黑白名单
     *
     * @param id
     * @return
     */
    @Override
    public void del(Long id) {
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper.selectById(id);
        if (customerAppClassify == null || customerAppClassify.getDeleteFlag()) {
            throw new BasicException(APP_CLASSIFY_NOT_EXIST_OR_DEL);
        }
        // 非管理员 需要校验客户id
        if (!checkCustomerIdLegitimacy(customerAppClassify.getCustomerId())) {
            throw new BasicException(CUSTOMER_ID_ILLEGALITY);
        }
        // 应用类型逻辑删
        CustomerAppClassify customerAppClassifyUpdate = new CustomerAppClassify();
        customerAppClassifyUpdate.setId(customerAppClassify.getId());
        customerAppClassifyUpdate.setDeleteFlag(true);
        customerAppClassifyUpdate.setUpdateBy(SecurityUtils.getUsername());
        customerAppClassifyUpdate.setUpdateTime(new Date());
        int customerAppClassifyStatus = customerAppClassifyMapper.updateById(customerAppClassifyUpdate);
        if (customerAppClassifyStatus > 0) {
            // 先获取原先的padCode
            List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper
                    .selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                            .eq("customer_id", customerAppClassify.getCustomerId())
                            .eq("app_classify_id", customerAppClassify.getId()));
            // 关联数据物理删
            customerAppClassifyRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                    .eq("customer_id", customerAppClassify.getCustomerId())
                    .eq("app_classify_id", customerAppClassify.getId()));
            customerAppClassifyPadRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                    .eq("customer_id", customerAppClassify.getCustomerId())
                    .eq("app_classify_id", customerAppClassify.getId()));

            boolean isBlack = customerAppClassify.getClassifyType() == 2;
            if (customerAppClassify.getApplyAllInstances() != null && !customerAppClassify.getApplyAllInstances()) {
                // 发送黑白名单指令任务
                sendAppWhiteAndBlackList(customerAppClassify.getCustomerId(), oldCustomerAppClassifyPadRelationList,
                        isBlack);
            } else {
                // 黑白名单 每次删除都要触发该客户下所有实例设置黑白名单
                List<PadInfoVO> pads = padMapper.selectValidPadCodeByCustomerId(customerAppClassify.getCustomerId());
                if (CollUtil.isNotEmpty(pads)) {
                    sendNewAppWhiteAndBlackList(customerAppClassify.getCustomerId(), pads, isBlack);
                }
            }
        }
    }


    /**
     * 构建黑白名单详情对象
     *
     * @param customerAppClassify
     * @param customerAppClassifyRelationList
     * @param customer
     * @return
     */
    private AppClassifyDetailVO buildAppClassifyDetailVO(CustomerAppClassify customerAppClassify,
            List<CustomerAppClassifyRelation> customerAppClassifyRelationList,
            Customer customer) {
        AppClassifyDetailVO appClassifyDetailVO = BeanUtil.copyProperties(customerAppClassify,
                AppClassifyDetailVO.class);
        appClassifyDetailVO.setCustomerName(customer.getCustomerName());
        appClassifyDetailVO.setCustomerAccount(customer.getCustomerAccount());
        if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
            List<AppClassifyDetailVO.AppInfo> appInfos = BeanUtil.copyToList(customerAppClassifyRelationList,
                    AppClassifyDetailVO.AppInfo.class);
            appClassifyDetailVO.setAppInfos(appInfos);
        }
        return appClassifyDetailVO;
    }

    /**
     * 构建黑白名单关联实例对象
     *
     * @param customerAppClassify
     * @param customerAppClassifyPadRelationList
     * @return
     */
    private AppClassifyPadDetailVO buildAppClassifyPadDetailVO(CustomerAppClassify customerAppClassify,
            List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList) {
        AppClassifyPadDetailVO appClassifyPadDetailVO = BeanUtil.copyProperties(customerAppClassify,
                AppClassifyPadDetailVO.class);
        if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
            List<AppClassifyPadDetailVO.AppPadInfo> appInfos = BeanUtil.copyToList(customerAppClassifyPadRelationList,
                    AppClassifyPadDetailVO.AppPadInfo.class);
            appClassifyPadDetailVO.setAppPadInfos(appInfos);
        }
        return appClassifyPadDetailVO;
    }

    /**
     * 构建黑白名单对象
     *
     * @param param
     * @return
     */
    private CustomerAppClassify buildCustomerAppClassify(Boolean isInsert, AppClassifySaveDTO param) {
        // 计算应用总数
        Integer appNum = 0;
        // if (CollUtil.isNotEmpty(param.getAppInfos())) {
        //     appNum += param.getAppInfos().size();
        // }
        CustomerAppClassify customerAppClassify = new CustomerAppClassify();
        customerAppClassify.setCustomerId(param.getCustomerId());
        customerAppClassify.setClassifyName(param.getClassifyName());
        customerAppClassify.setClassifyType(param.getClassifyType());
        customerAppClassify.setAppNum(appNum);
        customerAppClassify.setRemark(param.getRemark());
        if (isInsert) {
            customerAppClassify.setCreateBy(SecurityUtils.getUsername());
            customerAppClassify.setCreateTime(new Date());
        } else {
            customerAppClassify.setId(param.getId());
        }
        customerAppClassify.setUpdateBy(SecurityUtils.getUsername());
        customerAppClassify.setUpdateTime(new Date());
        customerAppClassify.setDeleteFlag(false);
        return customerAppClassify;
    }

    /**
     * 构建黑白名单关联应用对象
     *
     * @param param
     * @param customerAppClassifyId
     * @return
     */
    private List<CustomerAppClassifyRelation> buildCustomerAppClassifyRelation(AppClassifySaveDTO param,
            Long customerAppClassifyId) {
        List<CustomerAppClassifyRelation> newCustomerAppClassifyRelations = null;
        List<CustomerAppClassifyRelation> customerAppClassifyRelationList = BeanUtil.copyToList(param.getAppInfos(),
                CustomerAppClassifyRelation.class);
        if (CollUtil.isNotEmpty(customerAppClassifyRelationList)) {
            newCustomerAppClassifyRelations = new ArrayList<>();
            for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                if (customerAppClassifyRelation == null
                        || StrUtil.isEmpty(customerAppClassifyRelation.getPackageName())) {
                    continue;
                }
                customerAppClassifyRelation.setCustomerId(param.getCustomerId());
                customerAppClassifyRelation.setAppClassifyId(customerAppClassifyId);
                customerAppClassifyRelation.setCreateBy(SecurityUtils.getUsername());
                customerAppClassifyRelation.setCreateTime(new Date());
                customerAppClassifyRelation.setUpdateBy(SecurityUtils.getUsername());
                customerAppClassifyRelation.setUpdateTime(new Date());
                newCustomerAppClassifyRelations.add(customerAppClassifyRelation);
            }
        }
        return newCustomerAppClassifyRelations;
    }

    /**
     * 构建黑白名单关联实例对象
     *
     * @param param
     * @return
     */
    private List<CustomerAppClassifyPadRelation> buildCustomerAppClassifyPadRelation(AppClassifyPadSaveDTO param) {
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = BeanUtil
                .copyToList(param.getAppPadInfos(), CustomerAppClassifyPadRelation.class);
        if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
            for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList) {
                customerAppClassifyPadRelation.setCustomerId(param.getCustomerId());
                customerAppClassifyPadRelation.setAppClassifyId(param.getId());
                customerAppClassifyPadRelation.setCreateBy(SecurityUtils.getUsername());
                customerAppClassifyPadRelation.setCreateTime(new Date());
                customerAppClassifyPadRelation.setUpdateBy(SecurityUtils.getUsername());
                customerAppClassifyPadRelation.setUpdateTime(new Date());
            }
        }
        return customerAppClassifyPadRelationList;
    }

    /**
     * 校验客户id的合法性
     * 非管理员客户需要获取当前登录用户id和传入的客户id校验
     *
     * @param customerId
     * @return true校验通过 false校验不通过
     */
    private Boolean checkCustomerIdLegitimacy(Long customerId) {
        if (!SecurityUtils.isAdmin()) {
            return SecurityUtils.getUserId().equals(customerId);
        }
        return true;
    }

    /**
     * 发送黑白名单指令任务
     *
     * @param customerId
     * @param customerAppClassifyPadRelationList
     * @param isBlack
     */
    private void sendAppWhiteAndBlackList(Long customerId,
            List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList, boolean isBlack) {
        if (CollUtil.isNotEmpty(customerAppClassifyPadRelationList)) {
            // key为规格 value为实例编号
            Map<String, List<String>> map = new HashMap<>();
            // 以规格的维度发送黑白名单
            for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList) {
                List<String> padCodes = map.get(customerAppClassifyPadRelation.getDeviceLevel());
                if (padCodes == null) {
                    padCodes = new ArrayList<>();
                    map.put(customerAppClassifyPadRelation.getDeviceLevel(), padCodes);
                }
                padCodes.add(customerAppClassifyPadRelation.getPadCode());
            }
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                TriggeringBlackDTO triggeringBlackDTO = new TriggeringBlackDTO();
                triggeringBlackDTO.setIsNewApi(true);
                triggeringBlackDTO.setIsMergeAppClassifyList(true);
                triggeringBlackDTO.setCustomerId(customerId);
                triggeringBlackDTO.setPadCodes(entry.getValue());
                triggeringBlackDTO.setPadGrade(entry.getKey());
                triggeringBlackDTO.setSourceCode(SourceTargetEnum.ADMIN_SYSTEM);
                if (isBlack) {
                    padInternalFacade.triggeringBlacklist(triggeringBlackDTO);
                } else {
                    padInternalFacade.triggeringWhitelist(triggeringBlackDTO);
                }
            }
        }
    }

    /**
     * 发送黑白名单指令任务
     *
     * @param customerId
     * @param padInfoVOList
     * @param isBlack
     */
    private void sendNewAppWhiteAndBlackList(Long customerId, List<PadInfoVO> padInfoVOList, boolean isBlack) {
        whiteAndBlackThreadPool.submit(() -> {
            if (CollUtil.isNotEmpty(padInfoVOList)) {
                // key为规格 value为实例编号
                Map<String, List<String>> map = new HashMap<>();
                // 以规格的维度发送黑白名单
                for (PadInfoVO padInfoVO : padInfoVOList) {
                    List<String> padCodes = map.get(padInfoVO.getDeviceLevel());
                    if (padCodes == null) {
                        padCodes = new ArrayList<>();
                        map.put(padInfoVO.getDeviceLevel(), padCodes);
                    }
                    padCodes.add(padInfoVO.getPadCode());
                }
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    List<List<String>> padCodes = Lists.partition(entry.getValue(), 200);
                    for (List<String> subPadCodes : padCodes) {
                        TriggeringBlackDTO triggeringBlackDTO = new TriggeringBlackDTO();
                        triggeringBlackDTO.setIsNewApi(true);
                        triggeringBlackDTO.setIsMergeAppClassifyList(true);
                        triggeringBlackDTO.setCustomerId(customerId);
                        triggeringBlackDTO.setPadCodes(subPadCodes);
                        triggeringBlackDTO.setPadGrade(entry.getKey());
                        triggeringBlackDTO.setSourceCode(SourceTargetEnum.ADMIN_SYSTEM);
                        triggeringBlackDTO.setApplyAllInstances(true);
                        if (isBlack) {
                            padInternalFacade.triggeringBlacklist(triggeringBlackDTO);
                        } else {
                            padInternalFacade.triggeringWhitelist(triggeringBlackDTO);
                        }
                    }
                }
            }
        });
    }
}
