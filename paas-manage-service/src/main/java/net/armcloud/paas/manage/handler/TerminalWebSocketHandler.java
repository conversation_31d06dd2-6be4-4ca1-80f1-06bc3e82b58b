package net.armcloud.paas.manage.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.ChannelShell;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import net.armcloud.paas.manage.mapper.paas.EdgeClusterMapper;
import net.armcloud.paas.manage.model.LogCat;
import net.armcloud.paas.manage.model.WebSocketBO;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.ConnectAdbConstants.*;

@Slf4j
@Component
public class TerminalWebSocketHandler extends TextWebSocketHandler {

    //private final ExecutorService threadPool;
    @Autowired
    private ThreadPoolExecutor threadPool;
    @Autowired
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;
    @Autowired
    private EdgeClusterMapper edgeClusterMapper;
    @Resource
    private LogCat logCatType;
    private final Map<String, Session> sshSessions = new ConcurrentHashMap<>();
    private final Map<String, ChannelShell> sshChannels = new ConcurrentHashMap<>();
    private static final ThreadLocal<String> threadLocalType = new ThreadLocal<>();
    private final Map<String, List<String>> sshSessionsCommand = new ConcurrentHashMap<>();


    /**
     * 创建线程池
     */
    /*public TerminalWebSocketHandler() {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        this.threadPool = new ThreadPoolExecutor(
                availableProcessors * 2,
                availableProcessors * 3,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(2048),
                new ThreadFactoryBuilder().setNameFormat("webSocketPool-%d").build(),
                new ThreadPoolExecutor.AbortPolicy()
        );
    }*/

    /**
     * 连接websocket
     *
     * @param session
     * @throws Exception
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket connection established, session id: {}", session.getId());
        URI uri = session.getUri();
        if (uri == null) {
            log.error("WebSocket URI is null");
            session.close(CloseStatus.BAD_DATA);
            return;
        }

        Map<String, String> params = parseQueryParams(uri.getQuery());
        int cols = Integer.parseInt(params.getOrDefault("cols", "80"));
        int rows = Integer.parseInt(params.getOrDefault("rows", "24"));
        String deviceIp = params.get("deviceIp");
        String padCode = params.get("padCode");
        String padIp = params.get("padIp");
        String type = params.get("type");
        if (StrUtil.isBlank(deviceIp)) {
            log.error("Device IP is missing in WebSocket request");
            session.close(CloseStatus.BAD_DATA);
            return;
        }
        if (StrUtil.isBlank(padCode)&& type.equals(ENTER_INSTANCE)) {
            log.error("padCode is missing in WebSocket request");
            session.close(CloseStatus.BAD_DATA);
            return;
        }
        String sessionId = session.getId();
        // 创建SSH连接
        try {
            Session sshSession ;
            if(type.equals(ENTER_INSTANCE)){
                sshSession = setupPadSshSession(deviceIp);
            }else{
                if(logCatType.getIsRelay()) {
                    sshSession = setupJumpSshSession(sessionId,deviceIp);
                } else {
                    sshSession = setupSshSession(deviceIp);
                }
            }
            sshSessions.put(sessionId, sshSession);
            ChannelShell channel = setupChannel(sshSession, cols, rows);
            sshChannels.put(sessionId, channel);
            sshSessionsCommand.put(sessionId, new ArrayList<>());

            startInputStreamToWebSocket(session,type);
            //连接后执行进入实例的命令
            if(type.equals(PAD_LOG)){
                executeCommands(channel,tailLog("1",padCode));
                executeCommands(channel,tailLog("5",padCode));
            }else if(type.equals("1")){
                executeCommands(channel,tailLog("6",padIp));
                Thread.sleep(100);
                executeCommands(channel,tailLog("7",padIp));
            }else if(type.equals(DEVICE_SHELL)){
                log.info("padIp is {}",padIp);
            }else{
                executeCommands(channel,tailLog(type,padCode));
            }
        } catch (Exception e) {
            log.error("Failed to establish SSH session or channel", e);
            session.close(CloseStatus.SERVER_ERROR);
        }
    }

//    private Session setupPadSshSession() throws Exception {
//        JSch jsch = new JSch();
//        int port = 22;
//        if(!Objects.isNull(logCatType.getAdbPort())){
//            port = logCatType.getAdbPort();
//        }
//
//        Session sshSession = jsch.getSession(logCatType.getAdbUserName(), logCatType.getAdbIp(), port);
//        sshSession.setPassword(logCatType.getAdbUserPwd());
//        sshSession.setConfig("StrictHostKeyChecking", "no");
//        sshSession.connect();
//        return sshSession;
//    }

    private Session setupPadSshSession(String deviceIp) throws Exception {
        JSch jsch = new JSch();
        String edgeClusterCode = edgeClusterMapper.selectEdgeClusterCodeByDeviceIpSingle(deviceIp);
        String adbIp = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.ADB_PROXY_SERVER_IP);
        Integer adbPort = Integer.parseInt(edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.ADB_PROXY_SERVER_PORT));
        String adbUserName = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.ADB_PROXY_SERVER_USERNAME);
        String adbUserPwd = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.ADB_PROXY_SERVER_PASSWORD);

        Session sshSession = jsch.getSession(adbUserName, adbIp, adbPort);
        sshSession.setPassword(adbUserPwd);
        sshSession.setConfig("StrictHostKeyChecking", "no");
        sshSession.connect();
        return sshSession;
    }


    private String tailLog(String type , String padCode){
        switch (type){
            case ENTER_INSTANCE://进入实例
                return StrUtil.format(logCatType.getEnterDevice(),padCode,"\n");
            case KERNEL_LOG://内核日志
                return StrUtil.format(logCatType.getKernelLog(),"\n");
            case CBS_LOG://cbs日志
                return StrUtil.format(logCatType.getCbsLog(),  DateUtil.today(),"\n");
            case DEVICES_LOG://板卡日志
                return StrUtil.format(logCatType.getDeviceLog(),  "\n");
            case PAD_LOG://实例日志
                return StrUtil.format(logCatType.getPadLog(),  "\n");
            case ADB_CONNECT://adb连接
                return StrUtil.format(logCatType.getAdbConnect(),  padCode,"\n");
            case ADB_SHELL://adb连接
                return StrUtil.format(logCatType.getAdbShell(),  padCode,"\n");
            default: return null;
        }
    }

    /**
     * 板卡ssh连接
     *
     * @param deviceIp
     * @throws Exception
     */
    private Session setupSshSession(String deviceIp) throws Exception {
        JSch jsch = new JSch();
        Session sshSession = jsch.getSession(logCatType.getDeviceUserName(), deviceIp, 22);
        sshSession.setPassword(logCatType.getDeviceUserPwd());
        sshSession.setConfig("StrictHostKeyChecking", "no");
        sshSession.connect();
        return sshSession;
    }

    private Session setupJumpSshSession(String sessionId,String deviceIp) throws Exception {
        JSch jsch = new JSch();

        Session jumpHostSession = setupPadSshSession(deviceIp);

        int localPort = 12345;
        int remotePort = 22;
        jumpHostSession.setPortForwardingL(localPort, deviceIp, remotePort);
        sshSessions.put(sessionId + "-jump", jumpHostSession);

        Session sshSession = jsch.getSession(logCatType.getDeviceUserName(), "localhost", localPort);
        sshSession.setPassword(logCatType.getDeviceUserPwd());
        sshSession.setConfig("StrictHostKeyChecking", "no");
        sshSession.connect();

        return sshSession;
    }

    /**
     * 设置伪终端大小
     *
     * @param cols
     * @param rows
     * @throws Exception
     */
    private ChannelShell setupChannel(Session sshSession, int cols, int rows) throws Exception {
        ChannelShell channel = (ChannelShell) sshSession.openChannel("shell");
        channel.setPty(true);
        channel.setPtySize(cols, rows, 0, 0);
        channel.connect();
        return channel;
    }
    /**
     * 返回消息
     *
     * @param session
     */
    private void startInputStreamToWebSocket(WebSocketSession session,String type) {
        threadLocalType.set(type);
        String localType = threadLocalType.get();
        threadPool.submit(() -> {
            boolean flag = false;
            try {
                InputStream in = sshChannels.get(session.getId()).getInputStream();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1 && session.isOpen()) {
                    String output = new String(buffer, 0, bytesRead);
                    if(localType.equals(ENTER_INSTANCE)){
                        if (!flag && output.contains("/ #")) {
                            String[] lines = output.split("\n");
                            for (String line : lines) {
                                if (line.trim().endsWith("/ #") || line.trim().contains("Error")) {
                                    flag = true;
                                }
                            }
                        } else if (!flag && output.contains("/ $")) {
                            String[] lines = output.split("\n");
                            for (String line : lines) {
                                if (line.trim().endsWith("/ $") || line.trim().contains("Error")) {
                                    flag = true;
                                }
                            }
                        }

                        List<String> historyCommands= sshSessionsCommand.get(session.getId());
                        String histories =String.join("", historyCommands);
                        if(histories.contains("exit\r")) {
                           closeResources(session.getId());
                           session.close();
                           return;
                        }

                        if(flag){
                            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                        }
//                        session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                    }else if(localType.equals(PAD_LOG)){
                        if (!flag && output.contains("AC")) {
                            String[] lines = output.split("\n");
                            for (String line : lines) {
                                if (line.trim().startsWith("AC") || line.trim().contains("Error")) {
                                    flag = true;
                                }
                            }
                        }
                        if(flag){
                            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                        }
//                        session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                    } else if(localType.equals(DEVICE_SHELL)){
                        String[] lines = output.split("\n");
                        for (String line : lines) {
                            if (line.trim().contains("root") || line.trim().contains("Error")) {
                                flag = true;
                            }
                        }
                        if(flag){
                            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                        }
                    }else{
                        if (!flag && (output.contains("tail -f") || output.contains("logcat"))) {
                            String[] lines = output.split("\n");
                            for (String line : lines) {
                                if (!line.trim().contains("tail -f")|| line.trim().contains("Error")) {
                                    flag = true;
                                }
                            }
                        }
                        if(flag){
                            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                        }
//                        session.sendMessage(new TextMessage(JSONUtil.toJsonStr(new WebSocketBO("stdout", output))));
                    }
                }
            } catch (IOException e) {
                log.error("Error reading from SSH channel or sending WebSocket message: {}", e.getMessage());
            }
        });
    }

    /**
     * 处理通道消息
     *
     * @param session
     * @param message
     * @throws Exception
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String sessionId = session.getId();
        String command = message.getPayload();
        try {
            WebSocketBO webSocketBO = JSONUtil.toBean(command, WebSocketBO.class);
            String type = webSocketBO.getType();
            String data = webSocketBO.getData();
            List<String> historyCommands= sshSessionsCommand.get(sessionId);
            historyCommands.add(data);

            if ("resize".equals(type)) {
                JSONObject dataObject = JSONUtil.parseObj(data);
                int rows = dataObject.getInt("rows");
                int cols = dataObject.getInt("cols");
                adjustTerminalSize(sessionId, cols, rows);
//                session.sendMessage(new TextMessage("Terminal size adjusted to " + cols + " columns and " + rows + " rows"));
            } else {
                ChannelShell channel = sshChannels.get(sessionId);
                if (channel != null && channel.isConnected()) {
                    try {
                        OutputStream out = channel.getOutputStream();
                        out.write(data.getBytes());
                        out.flush();
                    } catch (Exception e) {
                        log.info("WebSocket connection error: " + e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            log.error("Error processing message: {}", e.getMessage());
            session.sendMessage(new TextMessage("Error processing message: " + e.getMessage()));
        }
    }


    private void adjustTerminalSize(String sessionId, int cols, int rows) throws IOException {
        ChannelShell channel = sshChannels.get(sessionId);
        if (channel != null && channel.isConnected()) {
            try {
                channel.setPtySize(cols, rows, 0, 0);
            } catch (Exception e) {
                log.error("Failed to adjust terminal size: {}", e.getMessage());
                throw new IOException("Failed to adjust terminal size", e);
            }
        }
    }

    /**
     * 关闭连接
     *
     * @param session
     * @param status
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        closeResources(sessionId);
    }

    /**
     * 关闭线程池，断开连接
     */
    @PreDestroy
    public void shutdown() {
        if (!threadPool.isShutdown()) {
            threadPool.shutdown();
        }
        sshSessions.keySet().forEach(this::closeResources);
    }

    /**
     * 断开连接
     */
    private void closeResources(String sessionId) {
        log.info("WebSocket Closing resources for session: {}", sessionId);
        ChannelShell channel = sshChannels.remove(sessionId);
        if (channel != null && channel.isConnected()) {
            channel.disconnect();
        }
        Session sshSession = sshSessions.remove(sessionId);
        if (sshSession != null && sshSession.isConnected()) {
            sshSession.disconnect();
        }
        Session sshJumpSession = sshSessions.remove(sessionId + "-jump");
        if (sshJumpSession != null && sshJumpSession.isConnected()) {
            sshJumpSession.disconnect();
        }
        sshSessionsCommand.remove(sessionId);
    }

    private Map<String, String> parseQueryParams(String query) {
        return Arrays.stream(query.split("&"))
                .map(param -> param.split("="))
                .collect(Collectors.toMap(
                        pair -> pair[0],
                        pair -> pair.length > 1 ? pair[1] : ""
                ));
    }

    private void executeCommands(ChannelShell channel, String... commands) throws IOException, InterruptedException {
        if (channel != null && channel.isConnected()) {
            OutputStream out = channel.getOutputStream();
            for (String command : commands) {
                out.write(command.getBytes());
                out.flush();
            }
        } else {
            log.warn("SSH channel is not connected, cannot execute commands");
        }
    }
}