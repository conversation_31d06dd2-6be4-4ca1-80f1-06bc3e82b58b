package net.armcloud.paas.manage.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 黑白名单查询对象
 */
@Data
public class AppClassifyQueryDTO extends PageDTO implements Serializable {

    @ApiModelProperty(value = "客户id")
    private Long customerId;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;

    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    private Integer classifyType;
}
