package net.armcloud.paas.manage.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class OperateCustomerConfigDTO implements Serializable {

    @NotNull(message = "客户id不能为空")
    private Long customerId;

    /**
     * 推流类型（1：火山；2：armcloud）
     */
    @NotNull(message = "推流类型不能为空")
    private Integer streamType;

    /**
     * P2P拉流方式
     */
    private String p2pPeerToPeerPushStream = "default";
}
