package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class NetWorkVirtualizeManageDTO implements Serializable {

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "SOC型号")
    private String socModel;



    @ApiModelProperty(value = "实例规格编码")
//    @NotBlank(message = "specificationCode cannot null")
    private String specificationCode;

    @ApiModelProperty(value = "镜像ID")
    @NotBlank(message = "imageId cannot null")
    private String imageId;

    @ApiModelProperty(value = "屏幕布局编码")
    @NotBlank(message = "screenLayoutCode cannot null")
    private String screenLayoutCode;

    @ApiModelProperty(value = "CPU是否隔离")
    private Boolean isolateCpu = true;

    @ApiModelProperty(value = "内存是否隔离")
    private Boolean isolateMemory = true;

    @ApiModelProperty(value = "存储是否限制")
    private Boolean isolateStorage = true;

    @ApiModelProperty(value = "是否随机adi模板")
    private Boolean randomADITemplates = false;

    @ApiModelProperty(value = "云机数量")
    @NotBlank(message = "number cannot null")
    private Integer number;

    @ApiModelProperty(value = "是否网存实例")
    private Integer netStorageResFlag;

    @ApiModelProperty(value = "真机模板Id")
    private Integer templateId;

    /**
     * 实例dns
     */
    private String dns;
    /**
     * 实例 adi
     */
    private String adiUrl;
    /**
     * adi密码
     */
    private String adiPassword;
    /**
     * 实例安卓系统属性
     */
    private String deviceAndroidProps;

    /**
     * 实例类型
     *
     */
    private String padType;


    /**
     * 存储大小(单位/GB)
     */
    private Integer storageSize;
}
