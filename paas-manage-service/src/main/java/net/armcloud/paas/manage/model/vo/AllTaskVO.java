package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AllTaskVO implements Serializable {
    /**
     * 主任务id
     */
    @ApiModelProperty(value = "主任务id")
    private Long masterTaskId;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Integer taskId;

    /**
     * 批次号id
     */
    @ApiModelProperty(value = "批次号id")
    private String taskBatchId;

    /**
     * 实例id
     */
    @ApiModelProperty(value = "实例id")
    private String instanceId;

    /**
     * 客户信息-客户账户
     */
    @ApiModelProperty(value = "客户信息-客户账户")
    private String customerAccount;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户信息-客户id
     */
    @ApiModelProperty(value = "客户信息-客户id")
    private Long customerId;

    /**
     * 任务信息-任务类型
     */
    @ApiModelProperty(value = "任务信息-任务类型")
    private Integer taskType;

    /**
     * 任务信息-任务类型名称
     */
    @ApiModelProperty(value = "任务信息-任务类型名称")
    private String taskTypeName;

    /**
     * 任务信息-任务来源
     */
    @ApiModelProperty(value = "任务信息-任务来源")
    private String taskSource;

    /**
     * 任务信息-任务来源名称
     */
    @ApiModelProperty(value = "任务信息-任务来源名称")
    private String taskSourceName;

    /**
     * 任务信息-创建时间
     */
    @ApiModelProperty(value = "任务信息-创建时间")
    private String createTime;

    /**
     * 任务信息-任务名称
     */
    @ApiModelProperty(value = "任务信息-任务名称")
    private String taskName;

    /**
     * 任务信息-客户应用id
     */
    @ApiModelProperty(value = "任务信息-客户应用id")
    private String appId;

    /**
     * 任务信息-应用名称
     */
    @ApiModelProperty(value = "任务信息-应用名称")
    private String appName;

    /**
     * 任务信息-应用包名
     */
    @ApiModelProperty(value = "任务信息-应用包名")
    private String packageName;

    /**
     * 任务信息-版本
     */
    @ApiModelProperty(value = "任务信息-版本")
    private String version;

    /**
     * 任务信息-客户关联文件id
     */
    @ApiModelProperty(value = "任务信息-客户关联文件id")
    private Long customerFileId;


    /**
     * 任务信息-文件id
     */
    @ApiModelProperty(value = "任务信息-文件id")
    private String fileId;

    /**
     * 任务信息-文件名称
     */
    @ApiModelProperty(value = "任务信息-文件名称")
    private String fileName;

    /**
     * 执行信息-执行状态
     */
    @ApiModelProperty(value = "执行信息-执行状态")
    private Integer executeStatus;

    /**
     * 执行信息-执行状态名称
     */
    @ApiModelProperty(value = "执行信息-执行状态名称")
    private String executeStatusName;

    /**
     * 执行信息-执行时间
     */
    @ApiModelProperty(value = "执行信息-执行时间")
    private String executeTime;

    /**
     * 执行信息-完成时间
     */
    @ApiModelProperty(value = "执行信息-完成时间")
    private String finishTime;

    @ApiModelProperty(value = "云机编号")
    private String deviceCode;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    private String taskContent;

    /**
     *
     */
    @ApiModelProperty(value = "任务镜像ID参数")
    private String imageId;

    /**
     * 执行任务的镜像ID
     */
    @ApiModelProperty(value = "执行任务的镜像ID")
    private String lastImageId;

    /**
     * 执行任务请求参数
     */
    @ApiModelProperty(value = "执行任务请求参数")
    private String contentJson;

    /**
     * 任务查询类型
     * 1板卡任务 2实例任务 3应用任务 4实例上传任务
     */
    @ApiModelProperty(value = "任务查询类型")
    private Integer taskSelectType;

    /**
     *
     */
    @ApiModelProperty(value = "任务镜像ID版本")
    private String imageIdRomVersion;

    /**
     * 执行任务的镜像ID
     */
    @ApiModelProperty(value = "执行任务的镜像ID版本")
    private String lastImageIdRomVersion;

    /**
     * 子任务id
     */
    @ApiModelProperty(value = "子任务id")
    private Long subTaskId;
}
