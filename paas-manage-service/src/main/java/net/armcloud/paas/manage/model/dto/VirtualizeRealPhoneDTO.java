package net.armcloud.paas.manage.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class VirtualizeRealPhoneDTO {
    @NotNull(message = "templateId cannot null")
    @ApiModelProperty(value = "云真机模板Id")
    private Long templateId;

    @ApiModelProperty(value = "实例规格编码")
    @NotBlank(message = "specificationCode cannot null")
    private String specificationCode;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "服务器编码集合")
    private List<String> armServerCode;

    @ApiModelProperty(value = "SOC型号")
    private String socModel;

    @ApiModelProperty(value = "云机Ip")
    private List<String> deviceIps;

    @ApiModelProperty(value = "镜像ID")
    @NotBlank(message = "imageId cannot null")
    private String imageId;

    @ApiModelProperty(value = "CPU是否隔离")
    private Boolean isolateCpu = true;

    @ApiModelProperty(value = "内存是否隔离")
    private Boolean isolateMemory = true;

    @ApiModelProperty(value = "存储是否限制")
    private Boolean isolateStorage = true;

    @ApiModelProperty(value = "云机数量")
    @NotNull(message = "number cannot null")
    private Integer number;

    /**
     * 实例dns
     */
    private String dns;

    /**
     * 实例安卓系统属性
     */
    private String deviceAndroidProps;
}
