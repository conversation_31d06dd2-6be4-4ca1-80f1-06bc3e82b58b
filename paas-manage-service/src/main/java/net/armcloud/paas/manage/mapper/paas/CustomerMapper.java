package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paas.manage.model.dto.CustomerDTO;
import net.armcloud.paas.manage.model.vo.CustomerSelectionVO;
import net.armcloud.paas.manage.model.vo.CustomerVO;
import net.armcloud.paascenter.common.model.entity.paas.Customer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param customer
     * @return
     */
    int insert(Customer customer);

    /**
     * 修改
     *
     * @param customer
     * @return
     */
    int updateByPrimaryKey(Customer customer);

    /**
     * 查看回调配置
     *
     * @param id
     * @return
     */
    int selectPullBackByKey(Long id);

    /**
     * 分页查询
     *
     * @param customerDto
     * @return
     */
    List<CustomerVO> selectPageList(CustomerDTO customerDto);

    /**
     * 查询账户信息
     *
     * @param id
     * @return
     */
    CustomerVO selectByPrimaryKey(Long id);

    /**
     * 查询账户信息
     *
     * @return
     */
    List<Long> selectByIdAndAccount(CustomerDTO customerDTO);

    /**
     * 根据用户名密码查询
     *
     * @param username
     * @param password
     */
    CustomerVO selectByNameAndPassword(@Param("username") String username, @Param("password") String password);

    /**
     * 根据账号和手机号查询
     *
     * @param customerAccount
     * @param customerTel
     * @return
     */
    boolean selectByTelAndAccount(@Param("customerAccount") String customerAccount, @Param("customerTel") Long customerTel);

    Customer selectById(Long customerTel);

    List<Customer> selectByIds(@Param("ids") List<Long> ids);

    List<CustomerSelectionVO> getCustomerSelection();

    void enableByPrimaryKey(Long id);

    List<CustomerVO> selectionListCustomer(@Param("customerId") Long customerId,@Param("customerName") String customerName,@Param("customerAccount") String customerAccount);

    void insertZero(Customer customer);

    void updateZero(Customer customer);

    /**
     * 通过用户名称查询该用户是否存在
     * @param customerName
     * @return
     */
    boolean selectByTelAndName(@Param("customerName") String customerName);

    List<CustomerVO> selectBatchByIds(@Param("ids") List<Long> ids);
}
