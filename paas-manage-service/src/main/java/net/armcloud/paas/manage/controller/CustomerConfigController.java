package net.armcloud.paas.manage.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paas.manage.model.dto.OperateCustomerConfigDTO;
import net.armcloud.paas.manage.service.ICustomerConfigService;
import net.armcloud.paas.manage.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.model.entity.paas.CustomerConfig;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/manage/customerConfig")
@Api(tags = "客户定制化配置")
public class CustomerConfigController {

    @Resource
    private ICustomerConfigService customerConfigService;

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "客户定制化配置详情", httpMethod = "GET", notes = "客户定制化配置详情")
    public Result<CustomerConfig> detail(Long customerId) {
        CustomerConfig customerConfig = customerConfigService.getOne(new QueryWrapper<CustomerConfig>().lambda().eq(CustomerConfig::getCustomerId, customerId).last("LIMIT 1"));
        return Result.ok(customerConfig);
    }

    @RequestMapping(value = "/operate", method = RequestMethod.POST)
    @ApiOperation(value = "客户定制化配置详情", httpMethod = "POST", notes = "客户定制化配置详情")
    public Result<CustomerConfig> operate(@RequestBody @Valid OperateCustomerConfigDTO dto) {
        return Result.ok(customerConfigService.operate(dto));
    }
}
