package net.armcloud.paas.manage.mapper.paas;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paas.manage.constant.DynamicDataSourceConstants;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户黑白名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Mapper
@DS(value = DynamicDataSourceConstants.paas)
public interface CustomerAppClassifyMapper extends BaseMapper<CustomerAppClassify> {

    int cusInsert(CustomerAppClassify customerAppClassify);

    int updatePadNum(@Param("id")Long id,@Param("padNum")Integer padNum);
}
