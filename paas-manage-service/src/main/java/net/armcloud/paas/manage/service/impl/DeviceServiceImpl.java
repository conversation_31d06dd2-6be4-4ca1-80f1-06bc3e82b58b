package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import net.armcloud.paas.manage.client.internal.dto.VirtualizeDeviceDTO;
import net.armcloud.paas.manage.client.internal.stub.DeviceInternalFeignStub;
import net.armcloud.paas.manage.client.internal.vo.GenerateDeviceTaskVO;
import net.armcloud.paas.manage.constant.PadConstants;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.utils.FeignUtils;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.constant.StatusConstant;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paascenter.common.model.entity.manage.PadOnlineAndOffline;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.IDeviceService;
import net.armcloud.paas.manage.service.IResourceSpecificationService;
import net.armcloud.paas.manage.service.ITaskService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.PadAllocationStatusConstants.ALLOCATION_FAIL;
import static net.armcloud.paas.manage.constant.PadAllocationStatusConstants.UNALLOCATED;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.BOARD_CORRESPONDING_DEVICE_NUMBER_NOT_EXIST;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.DEFAULT_GATEWAY_BOARD_NOT_EXIST;
import static net.armcloud.paas.manage.constant.NumberConsts.ONE;
import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@Service
public class DeviceServiceImpl implements IDeviceService {

    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private PadMapper padMapper;
    @Resource
    private CustomerDeviceRecordMapper customerDeviceRecordMapper;
    @Resource
    private CustomerDeviceMapper customerDeviceMapper;
    @Resource
    private DevicePadServiceMapper devicePadServiceMapper;
    @Resource
    private ArmServerMapper armServerMapper;
    @Resource
    private CustomerDeviceRecallMapper customerDeviceRecallMapper;
    @Resource
    private DeviceInternalFeignStub deviceInternalFeignStub;
    @Resource
    private PadRoomMapper padRoomMapper;
    @Resource
    private ITaskService taskService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private RealPhoneTemplateMapper realPhoneTemplateMapper;
    @Resource
    private ResourceSpecificationMapper resourceSpecificationMapper;
    @Resource
    private RedisService redisService;

    @Override
    public Page<DeviceVO> queryDeviceList(DeviceDTO deviceDTO) {
        PageHelper.startPage(deviceDTO.getPage(), deviceDTO.getRows());
        if (!SecurityUtils.isAdmin()) {
            deviceDTO.setCustomerId(SecurityUtils.getUserId());
        }
        deviceDTO.paramConvert();
        List<DeviceVO> deviceVOS = deviceMapper.queryDeviceList(deviceDTO);
        deviceVOS.forEach(deviceVO -> {
            if (StrUtil.isNotEmpty(deviceVO.getId())) {
                //查询是否已分配客户
                CustomerDevice customerDevice = customerDeviceMapper.selectByDeviceId(deviceVO.getId());
                if (null != customerDevice) {
                    deviceVO.setStatus(StatusConstant.ALLOCATED);
                    deviceVO.setStatusName(StatusConstant.ALLOCATED_NAME);
                } else {
                    deviceVO.setStatus(StatusConstant.NOT_ALLOCATED);
                    deviceVO.setStatusName(StatusConstant.NOT_ALLOCATED_NAME);
                }
                //查询板卡在线离线总数
                PadOnlineAndOffline padOnlineAndOffline = padMapper.selectByDeviceCodeOnline(deviceVO.getDeviceCode());
                deviceVO.setOnlineSum(padOnlineAndOffline.getOnlineSum());
                deviceVO.setOfflineSum(padOnlineAndOffline.getOfflineSum());
                deviceVO.setCount(padOnlineAndOffline.getCount());
                if (StringUtils.isNotBlank(deviceVO.getExtLifeTimeInfo())){
                    ExtLifeTimeInfoDTO extLifeTimeInfoDto = JSONUtil.toBean(deviceVO.getExtLifeTimeInfo(), ExtLifeTimeInfoDTO.class);
                    deviceVO.setExtLifeTimeInfo_A(extLifeTimeInfoDto.getEXT_CSD_DEVICE_LIFE_TIME_EST_TYP_A());
                    deviceVO.setExtLifeTimeInfo_B(extLifeTimeInfoDto.getEXT_CSD_DEVICE_LIFE_TIME_EST_TYP_B());
                }
            }
        });
        return new Page<>(deviceVOS);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Result<?> allocateResource(SourceDTO param) {
        List<String> deviceIds = param.getDeviceId();
        Long groupId = param.getGroupId();
        Long customerId = param.getCustomerId();
        Date startTime = param.getStartTime();
        Date endTime = param.getEndTime();
        //添加customer_device表关联，添加customer_device_record表记录
        if (null != deviceIds && !deviceIds.isEmpty()) {
            for (String deviceId : deviceIds) {
                CustomerDevice byDeviceId = customerDeviceMapper.selectByDeviceId(deviceId);
                if (null != byDeviceId) {
                    throw new BasicException("资源已分配");
                }
                customerDeviceSaveOrUpdate(deviceId, customerId, startTime, endTime);
                //添加customer_device_record表记录
                customerDeviceSaveOrUpdateRecord(deviceId, customerId, startTime, endTime);
                List<Long> padList = devicePadServiceMapper.queryPadIdByDeviceIds(Collections.singletonList(deviceId));
                if (null != padList && !padList.isEmpty()) {
                    padList.forEach(padId -> {
                        Pad pad = padMapper.getById(padId);
                        if (pad != null) {
                            pad.setCustomerId(customerId);
                            pad.setGroupId(groupId);
                            padMapper.updateById(pad);
                        }
                    });
                }
            }
        }
        return Result.ok();
    }

    private void customerDeviceSaveOrUpdateRecord(String deviceId, Long customerId, Date startTime, Date endTime) {
        CustomerDeviceRecord byDeviceId = customerDeviceRecordMapper.selectByDeviceId(deviceId);
        if (null != byDeviceId) {
            byDeviceId.setCustomerId(customerId);
            byDeviceId.setStartTime(startTime);
            byDeviceId.setExpirationTime(endTime);
            customerDeviceRecordMapper.update(byDeviceId);
        } else {
            CustomerDeviceRecord customerDeviceRecord = new CustomerDeviceRecord();
            customerDeviceRecord.setCustomerId(customerId);
            customerDeviceRecord.setDeviceId(Long.valueOf(deviceId));
            customerDeviceRecord.setStartTime(startTime);
            customerDeviceRecord.setExpirationTime(endTime);
            customerDeviceRecord.setDeleteFlag(StatusConstant.NOT_DELETED);
            customerDeviceRecord.setCreateBy(SecurityUtils.getUsername());
            customerDeviceRecord.setCreateTime(new Date());
            customerDeviceRecordMapper.insert(customerDeviceRecord);
        }
    }

    private void customerDeviceSaveOrUpdate(String deviceId, Long customerId, Date startTime, Date endTime) {
        CustomerDevice byDeviceId = customerDeviceMapper.selectByDeviceId(deviceId);
        if (null != byDeviceId) {
            byDeviceId.setCustomerId(customerId);
            byDeviceId.setStartTime(startTime);
            byDeviceId.setExpirationTime(endTime);
            customerDeviceMapper.update(byDeviceId);
        } else {
            CustomerDevice customerDevice = new CustomerDevice();
            customerDevice.setCustomerId(customerId);
            customerDevice.setDeviceId(Long.valueOf(deviceId));
            customerDevice.setStartTime(startTime);
            customerDevice.setExpirationTime(endTime);
            customerDevice.setDeleteFlag(StatusConstant.NOT_DELETED);
            customerDevice.setCreateBy(SecurityUtils.getUsername());
            customerDevice.setCreateTime(new Date());
            customerDeviceMapper.insert(customerDevice);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoveryResource(List<String> deviceId) {
        List<Long> padList = devicePadServiceMapper.queryPadIdByDeviceIds(deviceId);
        List<String> padCodes = new ArrayList<>();
        if (!padList.isEmpty()) {
            padList.forEach(padId -> {
                Pad pad = padMapper.getById(padId);
                if (pad != null) {
                    if(Objects.equals(pad.getNetStorageResFlag(),1)){
                        PadDTO padDTO = new PadDTO();
                        padDTO.setPadCode(pad.getPadCode());
                        List<PadVO> padVOS = padMapper.listPads(padDTO);
                        throw new BasicException("板卡上"+padVOS.get(0).getPadCode()+"有绑定的网存实例,无法回收.请先前往实例列表，关机并删除该板卡上的所有实例后，再进行回收");
                    }
                    pad.setCustomerId(null);
                    pad.setGroupId(null);
                    pad.setUpdateTime(new Date());
                    padMapper.updatePadRecoveryResource(pad);
                    padCodes.add(pad.getPadCode());
                }
            });
        }

        //添加回收记录
        List<DeviceVO> devices = deviceMapper.selectBatchById(deviceId);
        if (CollUtil.isNotEmpty(devices)) {
            customerDeviceRecallMapper.batchInsert(devices);
        }
        //删除customer_device表关联，删除customer_device_record表记录
        customerDeviceMapper.deleteByDeviceId(deviceId);
        customerDeviceRecordMapper.deleteByDeviceId(deviceId);
        //清除板卡所在的网存计算单元信息,如果是本地实例,这里会清除为0,没有影响
        devicePadServiceMapper.clearNetStorageComputeUnit(deviceId);
    }

    @Override
    public List<CustomerVO> queryCustomerList(String query) {
        CustomerDTO customerDTO = SearchCalibrationUtil.customerSearch(query);
        customerDTO.setStatus(StatusConstant.CUSTOMER_STATUS_ENABLE);
        List<CustomerVO> customerVOS = customerMapper.selectPageList(customerDTO);
        return customerVOS;
    }

    @Override
    public Page<DeviceVO> allocateResourceRecord(DeviceDTO deviceDTO) {
        PageHelper.startPage(deviceDTO.getPage(), deviceDTO.getRows());
        List<DeviceVO> deviceVOS = customerDeviceRecordMapper.queryDeviceList(deviceDTO);
        deviceVOS.forEach(deviceVO -> {
            if (StrUtil.isNotEmpty(deviceVO.getId())) {
                //查询是否已分配客户
                CustomerDevice customerDevice = customerDeviceMapper.selectByDeviceId(deviceVO.getId());
                if (null != customerDevice) {
                    deviceVO.setStatus(StatusConstant.ALLOCATED);
                    deviceVO.setStatusName(StatusConstant.ALLOCATED_NAME);
                } else {
                    deviceVO.setStatus(StatusConstant.NOT_ALLOCATED);
                    deviceVO.setStatusName(StatusConstant.NOT_ALLOCATED_NAME);
                }
            }
        });
        return new Page<>(deviceVOS);
    }

    @Override
    public List<DeviceInfoVo> selectDeviceInfoByDeviceCode(List<String> deviceCodes, Long customerId) {
        return deviceMapper.selectDeviceInfoByDeviceCode(deviceCodes, customerId);
    }

    @Override
    public void timeRecoveryResource(List<String> deviceIds, Date recoveryTime) {
        customerDeviceMapper.batchUpdateRecoveryTime(deviceIds, recoveryTime);
        customerDeviceRecordMapper.batchUpdateRecoveryTime(deviceIds, recoveryTime);
    }

    /**
     * 下拉选择云机列表
     *
     * @param param
     * @return
     */
    @Override
    public List<SelectionListDeviceVo> selectionListDevice(SelectionListDeviceDTO param) {
        if (CollUtil.isEmpty(param.getArmServerCodes()) && (isNotEmpty(param.getSocModelCode()) || isNotEmpty(param.getClusterCode()))) {
            ArmServerDTO par = new ArmServerDTO();
            if (isNotEmpty(param.getSocModelCode())) {
                par.setSocModelCode(param.getSocModelCode());
            }
            if (isNotEmpty(param.getClusterCode())) {
                par.setClusterCode(param.getClusterCode());
            }
            List<SelectionArmServerVO> selectionArmServers = armServerMapper.selectionListArmServer(par);
            List<String> collect = selectionArmServers.stream().filter(r -> r.getCanCreateDeviceNum() > ZERO).map(SelectionArmServerVO::getArmServerCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                param.setArmServerCodes(collect);
            }
        }
        if (!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        return deviceMapper.selectionListDevice(param);
    }

    /**
     * 实例已分配物理机列表
     *
     * @param param
     * @return
     */
    @Override
    public List<String> padAllocationDeviceList(PadAllocationDeviceDTO param) {
        return deviceMapper.padAllocationDeviceList(param);
    }

    @Override
    public List<DeviceVO> getDeviceInfo(List<String> deviceIps) {
        return deviceMapper.getDeviceInfo(deviceIps);
    }

    @Override
    public Page<DeviceVO> deviceRecall(DeviceDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<DeviceVO> deviceVOS = customerDeviceRecallMapper.queryDeviceList(param);
        deviceVOS.forEach(deviceVO -> {
            if (StrUtil.isNotEmpty(deviceVO.getId())) {
                //查询是否已分配客户
                CustomerDevice customerDevice = customerDeviceMapper.selectByDeviceId(deviceVO.getId());
                if (null != customerDevice) {
                    deviceVO.setStatus(StatusConstant.ALLOCATED);
                    deviceVO.setStatusName(StatusConstant.ALLOCATED_NAME);
                } else {
                    deviceVO.setStatus(StatusConstant.NOT_ALLOCATED);
                    deviceVO.setStatusName(StatusConstant.NOT_ALLOCATED_NAME);
                }
            }
        });
        return new Page<>(deviceVOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByDeviceCodeAndPadCode(DeleteByDeviceCodeAndPadCodeDTO param) {
        List<String> deviceCodeList = param.getDeviceCodeList();
        List<String> padCodeList = param.getPadCodeList();
        if (CollUtil.isNotEmpty(deviceCodeList)) {
            //数据删除，非逻辑删除
            List<Device> devices = deviceMapper.selectByDeviceCode(deviceCodeList);
            //将devices中的id通过stream流转为list
            List<String> deviceIds = new ArrayList<>();
            for (Device device1 : devices) {
                Long id = device1.getId();
                deviceIds.add(String.valueOf(id));
            }
            List<Long> padIds = devicePadServiceMapper.queryPadIdByDeviceIds(deviceIds);
            List<Pad> pads = padMapper.selectByIds(padIds);
            List<String> padCodes = pads.stream().map(Pad::getPadCode).collect(Collectors.toList());
            deviceMapper.deleteByDeviceIds(deviceIds);
            padMapper.deleteByPadCodes(padCodes);
            padRoomMapper.deleteByPadeCodes(padCodes);
        } else if (CollUtil.isNotEmpty(padCodeList)) {
            List<Pad> pads = padMapper.selectByPadCodes(padCodeList);
            List<String> padCodes = pads.stream().map(Pad::getPadCode).collect(Collectors.toList());
            List<Long> padIds = pads.stream().map(Pad::getId).collect(Collectors.toList());
            List<String> deviceIds = devicePadServiceMapper.selectDeviceIdByPadIds(padIds);

            deviceMapper.deleteByDeviceIds(deviceIds);
            padMapper.deleteByPadCodes(padCodes);
            padRoomMapper.deleteByPadeCodes(padCodes);
        }
    }

    @Override
    public List<PadOnlineAndOffline> queryDeviceListOnlineAndOffline() {
        return padMapper.selectByDeviceCode(null);
    }

    @Override
    public DeviceGatewayVO getDeviceGatewayInfo(String deviceCode) {
        DeviceGatewayVO deviceGatewayInfo = deviceMapper.selectGatewayByCode(deviceCode);
        if (ObjectUtils.isEmpty(deviceGatewayInfo)) {
            throw new BasicException(BOARD_CORRESPONDING_DEVICE_NUMBER_NOT_EXIST);
        }
        if (StrUtil.isNotEmpty(deviceGatewayInfo.getGateway())) {
            return deviceGatewayInfo;
        }
        CardGatewayVO cardGatewayInfo = armServerMapper.selectCardGatewayByServerCode(deviceGatewayInfo.getArmServerCode());
        if (ObjectUtils.isEmpty(cardGatewayInfo)) {
            throw new BasicException(DEFAULT_GATEWAY_BOARD_NOT_EXIST);
        }
        deviceGatewayInfo.setGateway(cardGatewayInfo.getGateway());
        return deviceGatewayInfo;
    }

    @Override
    public String virtualizeRealPhone(VirtualizeRealPhoneDTO param) {
        RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.getById(param.getTemplateId());
        if (realPhoneTemplate == null) {
            throw new BasicException("模板不存在");
        }

        VirtualizeDTO virtualizeDTO = new VirtualizeDTO();
        BeanUtils.copyProperties(param, virtualizeDTO);
        // virtualizeDTO.setSpecificationCode(realPhoneTemplate.getResourceSpecificationCode());
        virtualizeDTO.setSpecificationCode(param.getSpecificationCode());
        virtualizeDTO.setScreenLayoutCode(realPhoneTemplate.getScreenLayoutCode());
        virtualizeDTO.setAdiUrl(realPhoneTemplate.getAdiTemplateDownloadUrl());
        virtualizeDTO.setAdiPassword(realPhoneTemplate.getAdiTemplatePwd());
        virtualizeDTO.setPadType(PadConstants.Type.REAL.getValue());
        virtualizeDTO.setRealPhoneTemplateId(param.getTemplateId());
        return virtualizeDevice(virtualizeDTO);
    }

    @Override
    public String virtualizeDevice(VirtualizeDTO param) {
        log.info("创建云机 virtualizeDevice param:{}", JSON.toJSONString(param));
        if (CollUtil.isEmpty(param.getDeviceIps()) && (isEmpty(param.getClusterCode()) || isEmpty(param.getSocModel()))) {
            throw new BasicException("请求参数有误！");
        }
        if (CollUtil.isNotEmpty(param.getDeviceIps()) && param.getDeviceIps().size() != param.getNumber()) {
            throw new BasicException("请求参数有误！");
        }
        if (param.getNumber() < ONE) {
            throw new BasicException("请求参数有误！");
        }
        if (CollUtil.isEmpty(param.getDeviceIps())) {
            SelectionListDeviceDTO par = new SelectionListDeviceDTO();
            par.setSocModelCode(param.getSocModel());
            par.setClusterCode(param.getClusterCode());
            par.setArmServerCodes(param.getArmServerCode());
            par.setNumber(param.getNumber());
            List<SelectionListDeviceVo> selectionListDeviceVos = selectionListDevice(par);
            if (io.jsonwebtoken.lang.Collections.isEmpty(selectionListDeviceVos)) {
                throw new BasicException("无有效板卡！");
            }
            List<String> deviceIps = selectionListDeviceVos.stream().map(SelectionListDeviceVo::getDeviceIp).collect(Collectors.toList());
            param.setDeviceIps(deviceIps);
        }
        List<DeviceVO> deviceList = getDeviceInfo(param.getDeviceIps());
        if (io.jsonwebtoken.lang.Collections.isEmpty(deviceList)) {
            throw new BasicException("无有效板卡！");
        }

        List<Integer> failStatusList = Arrays.asList(UNALLOCATED.getStatus(), ALLOCATION_FAIL.getStatus());
        StringBuilder failureMsg = new StringBuilder();
        List<String> deviceIps = new ArrayList<>();
        List<String> offlineDeviceIps = new ArrayList<>();
        for (DeviceVO device : deviceList) {
            if (!device.getStatus().equals(ONE)) {
                offlineDeviceIps.add(device.getDeviceIp());
            } else if (failStatusList.contains(device.getPadAllocationStatus())) {
                deviceIps.add(device.getDeviceIp());
            }
        }
        if (CollUtil.isNotEmpty(offlineDeviceIps)) {
            failureMsg.append(offlineDeviceIps.size() + "个板卡状态离线！");
        }
        if (io.jsonwebtoken.lang.Collections.isEmpty(deviceIps)) {
            throw new BasicException(failureMsg.toString() + "创建实例个数0个！");
        }
        IResourceSpecificationService resourceSpecificationService = applicationContext.getBean(IResourceSpecificationService.class);
        net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification resourceSpecification = resourceSpecificationService
                .getOne(new QueryWrapper<ResourceSpecification>().eq("specification_code", param.getSpecificationCode()).eq("delete_flag", ZERO).last("LIMIT 1"));

        deviceIps = deviceIps.stream().distinct().collect(Collectors.toList());
        VirtualizeDeviceDTO req = new VirtualizeDeviceDTO();
        BeanUtils.copyProperties(param, req);
        req.setCustomerId(SecurityUtils.getUserId());
        req.setSourceCode(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        req.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        req.setDeviceIps(deviceIps);
        req.setNetStorageResFlag(param.getNetStorageResFlag());
        req.setStorageSize(param.getStorageSize());
        String padType = StringUtils.isBlank(param.getPadType()) ? PadConstants.Type.VIRTUAL.getValue() : param.getPadType();
        req.setPadType(padType);
        log.info("创建云机 req:{}", JSON.toJSONString(req));
        Result<?> virtualize = deviceInternalFeignStub.virtualize(req);
        if (virtualize.getCode() == Result.SUCCESS) {
            List<GenerateDeviceTaskVO> data = (List<GenerateDeviceTaskVO>) virtualize.getData();
            long count = data.stream().filter(dto -> isNotEmpty(dto.getTaskId())).count();
            return failureMsg.toString() + count + "个板卡创建" + resourceSpecification.getPadNumber() + "开实例！预计创建时间30分钟左右";
        } else {
            throw new BasicException(failureMsg.toString() + virtualize.getMsg());
        }
    }

    @Override
    public List<Map<String, String>> queryDeviceStatus(List<String> deviceCodes) {
        if (CollUtil.isEmpty(deviceCodes)) {
            return new ArrayList<>();
        }

        List<Map<String, String>> deviceStatusList = new ArrayList<>();
        List<Device> devices = deviceMapper.selectByDeviceCode(deviceCodes);

        if (CollUtil.isNotEmpty(devices)) {
            // 返回的map里面要放 DeviceStatus + PadAllocationStatus
            devices.forEach(device -> {
                Map<String, String> map = new HashMap<>();
                map.put("deviceCode", device.getDeviceCode());
                map.put("deviceStatus", device.getDeviceStatus() + "");
                map.put("padAllocationStatus", device.getPadAllocationStatus() + "");
                deviceStatusList.add(map);
            });
        }

        return deviceStatusList;
    }

    @Override
    public String setDeviceLevel(DeviceLevelDTO param) {
        FeignUtils.getContent(deviceInternalFeignStub.setDeviceLevel(param));
        return StringUtils.EMPTY;

    }

    @Override
    public List<DeviceVO> getDeviceLevel(DeviceLevelDTO param) {
        return deviceMapper.getDeviceLevel(param);
    }
}
