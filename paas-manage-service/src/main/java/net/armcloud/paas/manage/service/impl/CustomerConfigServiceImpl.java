package net.armcloud.paas.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paas.manage.mapper.paas.CustomerConfigMapper;
import net.armcloud.paas.manage.mapper.paas.PadMapper;
import net.armcloud.paas.manage.model.dto.OperateCustomerConfigDTO;
import net.armcloud.paas.manage.service.ICustomerConfigService;
import net.armcloud.paascenter.common.model.entity.paas.CustomerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
public class CustomerConfigServiceImpl extends ServiceImpl<CustomerConfigMapper, CustomerConfig> implements ICustomerConfigService {

    @Autowired
    private PadMapper padMapper;

    /**
     * 客户定制化配置
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CustomerConfig operate(OperateCustomerConfigDTO dto) {
        CustomerConfig par = new CustomerConfig();
        par.setStreamType(dto.getStreamType());
        par.setP2pPeerToPeerPushStream(dto.getP2pPeerToPeerPushStream());

        CustomerConfig customerConfig = this.baseMapper.selectOne(new QueryWrapper<CustomerConfig>().lambda().eq(CustomerConfig::getCustomerId, dto.getCustomerId()).last("LIMIT 1"));
        if (isEmpty(customerConfig)) {
            //新增定制
            par.setCustomerId(dto.getCustomerId());
            par.setCreateTime(new Date());
            this.baseMapper.insert(par);
            //刷新实例推流方式
            padMapper.updateStreamTypeByCustomerId(dto.getStreamType(), dto.getCustomerId());
        } else {
            if (!dto.getStreamType().equals(customerConfig.getStreamType())) {
                //刷新实例推流方式
                padMapper.updateStreamTypeByCustomerId(dto.getStreamType(), dto.getCustomerId());
            }
            par.setId(customerConfig.getId());
            par.setUpdateTime(new Date());
            this.baseMapper.updateById(par);
        }
        return null;
    }
}
