package net.armcloud.paas.manage.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.vo.PadGroupVO;
import net.armcloud.paas.manage.model.vo.PadVO;
import net.armcloud.paas.manage.model.vo.SelectionResourceSpecificationVO;
import net.armcloud.paas.manage.service.IPadService;
import net.armcloud.paas.manage.service.IResourceSpecificationService;
import net.armcloud.paas.manage.service.IpadGroupService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@RestController
@RequestMapping("/manage/padManage")
@Api(tags = "实例管理")
public class PadManageController {
    @Resource
    private IPadService padService;
    @Resource
    private IpadGroupService padGroupService;
    @Resource
    private IResourceSpecificationService resourceSpecificationService;

    @RequestMapping(value = "/queryPadList", method = RequestMethod.POST)
    @ApiOperation(value = "实例列表", httpMethod = "POST", notes = "实例列表")
    public Result<Page<PadVO>> queryPadList(@RequestBody PadDTO param) {
        PadDTO padDTO = SearchCalibrationUtil.padSearch(param);
        padDTO.setCustomerId(SecurityUtils.getUserId());
        Page<PadVO> deviceVOS = padService.listPads(padDTO);
        return Result.ok(deviceVOS);
    }

    @ApiOperation(value = "分组列表", httpMethod = "POST", notes = "分组列表")
    @RequestMapping(value = "/queryGroup", method = RequestMethod.POST)
    public Result<Page<PadGroupVO>> queryGroup(@RequestBody PadGroupDTO param) {
        if(!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        PadGroupDTO padGroupDTO = SearchCalibrationUtil.groupQuery(param);
        Page<PadGroupVO> padGroups = padGroupService.queryPadGroup(padGroupDTO);
        return Result.ok(padGroups);
    }

    @ApiOperation(value = "分组编辑", httpMethod = "POST", notes = "分组编辑")
    @RequestMapping(value = "/updateGroup", method = RequestMethod.POST)
    public Result<Page<PadGroupVO>> updateGroup(@RequestBody PadGroupDTO param) {
        if(!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        param.setOprBy(SecurityUtils.getCustomerCode()+"_"+SecurityUtils.getUserPhoneNumber());
        padGroupService.updateGroup(param);
        return Result.ok();
    }

    @ApiOperation(value = "新增分组", httpMethod = "POST", notes = "新增分组")
    @RequestMapping(value = "/insertGroup", method = RequestMethod.POST)
    public Result<?> insertGroup(@RequestBody PadGroupDTO param) {
        return padGroupService.addPadGroup(param);
    }

    @ApiOperation(value = "实例分配", httpMethod = "POST", notes = "实例分配")
    @RequestMapping(value = "/allocateResource", method = RequestMethod.POST)
    public Result<?> allocateResource(@RequestBody PadGroupDTO param) {
        if(!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        param.setOprBy("实例分配："+SecurityUtils.getCustomerCode()+"_"+SecurityUtils.getUserPhoneNumber());
        padService.allocatePad(param);
        return Result.ok(null, "分配成功");
    }

    @ApiOperation(value = "实例回收", httpMethod = "POST", notes = "实例回收")
    @RequestMapping(value = "/padRecall", method = RequestMethod.POST)
    public Result<?> padRecall(@RequestBody PadGroupDTO param) {
        if(!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        param.setGroupId(0L);
        param.setOprBy("实例回收："+SecurityUtils.getCustomerCode()+"_"+SecurityUtils.getUserPhoneNumber());
        padService.allocatePad(param);
        return Result.ok(null, "已回收");
    }

    @ApiOperation(value = "一键回收", httpMethod = "POST", notes = "一键回收")
    @RequestMapping(value = "/padRecallAll", method = RequestMethod.POST)
    public Result<?> padRecallAll(@RequestBody PadGroupDTO param) {
        if(!SecurityUtils.isAdmin()){
            param.setCustomerId(SecurityUtils.getUserId());
        }
        param.setGroupId(0L);
        param.setOprBy("一键回收："+SecurityUtils.getCustomerCode()+"_"+SecurityUtils.getUserPhoneNumber());
        padService.allocatePad(param);
        return Result.ok(null, "已回收");
    }

    @ApiOperation(value = "实例分组id", httpMethod = "GET", notes = "实例分组id")
    @RequestMapping(value = "/padMaxId", method = RequestMethod.GET)
    public Result<?> padMaxId(@RequestParam(required = false) Long customerId) {
        if(Objects.isNull(customerId)){
            customerId= SecurityUtils.getUserId();
        }
        int maxId = padGroupService.padMaxId(customerId);
        return Result.ok(maxId);
    }

    @ApiOperation(value = "删除分组", httpMethod = "DELETE", notes = "删除分组")
    @RequestMapping(value = "/deletePadGroup", method = RequestMethod.DELETE)
    public Result<?> deletePadGroup(Long id,@RequestParam(required = false) Long customerId) {
        if(Objects.isNull(customerId)){
            customerId = SecurityUtils.getUserId();
        }
        if(Objects.isNull(customerId)){
            return Result.fail("用户Id不能为空");
        }

        return padGroupService.deletePadGroup(id,customerId);
    }

    @RequestMapping(value = "/selectionList", method = RequestMethod.POST)
    @ApiOperation(value = "下拉实例规格列表", httpMethod = "POST", notes = "下拉实例规格列表")
    public Result<List<SelectionResourceSpecificationVO>> selectionList(@RequestBody SelectionResourceSpecificationDTO param) {
        return Result.ok(resourceSpecificationService.selectionList(param));
    }

    @RequestMapping(value = "/netStorageRes/padCode/off", method = RequestMethod.POST)
    @ApiOperation(value = "实例关机", httpMethod = "POST", notes = "实例关机")
    public Result<String> netStorageResBootOff(@RequestBody NetStorageResBootDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        padService.netStorageResBootOff(param);
        return Result.ok();
    }

    @RequestMapping(value = "/netStorageRes/padCode/on", method = RequestMethod.POST)
    @ApiOperation(value = "实例开机", httpMethod = "POST", notes = "实例开机")
    public Result<String> netStorageResOn(@RequestBody NetStorageResBootDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        padService.netStorageResBootOn(param);
        return Result.ok();
    }

    @RequestMapping(value = "/netStorageRes/padCode/delete", method = RequestMethod.POST)
    @ApiOperation(value = "实例删除(回收网存)", httpMethod = "POST", notes = "实例删除(回收网存)")
    public Result<String> netStorageResDelete(@RequestBody NetStorageResBootDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        padService.netStorageResDelete(param);
        return Result.ok();
    }


    @RequestMapping(value = "/netStorageRes/padCode/moreCompatible", method = RequestMethod.POST)
    @ApiOperation(value = "实例更配()", httpMethod = "POST", notes = "实例更配")
    public Result<String> netStorageResMoreCompatible(@RequestBody NetStorageResMoreCompatibleDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        return Result.ok( padService.netStorageResMoreCompatible(param));
    }


}
