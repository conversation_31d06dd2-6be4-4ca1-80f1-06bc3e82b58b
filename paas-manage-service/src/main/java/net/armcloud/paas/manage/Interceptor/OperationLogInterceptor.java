package net.armcloud.paas.manage.Interceptor;

import net.armcloud.paas.manage.context.SecurityContextHolder;
import net.armcloud.paas.manage.mapper.paas.OperationLogsMapper;
import net.armcloud.paas.manage.wrapper.CachedBodyHttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.manage.OperationLog;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/2/19 22:02
 * @Version 1.0
 */
@Component
@Slf4j
public class OperationLogInterceptor implements HandlerInterceptor {

    /**
     * 开始时间
     */
    private long startTime;

    private final OperationLogsMapper operationLogsMapper;

    public OperationLogInterceptor(OperationLogsMapper operationLogsMapper) {
        this.operationLogsMapper = operationLogsMapper;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        startTime = System.currentTimeMillis();
        return true;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

        try { // 计算请求处理的时间
            StringBuilder builder = new StringBuilder();
            String method = request.getMethod();
            /**
             * 接口耗时以及接口响应状态异常消息
             */
            long duration = System.currentTimeMillis() - startTime;
            int status = response.getStatus();
            builder.append("{\n");
            // 获取 URL 查询参数（即GET请求的query parameters）
            String queryParams = request.getQueryString();
            if (queryParams != null) {
                builder.append("\"query_params\": {");
                String[] pairs = queryParams.split("&");
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=");
                    if (keyValue.length == 2 && keyValue[1] != null && !keyValue[1].isEmpty()) {
                        builder.append("\"").append(keyValue[0]).append("\": \"").append(keyValue[1]).append("\", ");
                    }
                }
                if (builder.charAt(builder.length() - 2) == ',') {
                    builder.setLength(builder.length() - 2);
                }
                builder.append("},\n");
            }

            // 获取请求 Body 内容（适用于 POST、PUT、PATCH 等请求）
            if (request.getMethod().equalsIgnoreCase("POST") ||
                    request.getMethod().equalsIgnoreCase("PUT") ||
                    request.getMethod().equalsIgnoreCase("PATCH")) {
                String body = getRequestBody(request);
                if (body != null && !body.isEmpty()) {
                    builder.append("\"body\": ").append(body).append(",\n");
                }

                // 获取表单参数（application/x-www-form-urlencoded 或 multipart/form-data）
                Map<String, String[]> formParams = request.getParameterMap();
                if (!formParams.isEmpty()) {
                    builder.append("\"form_params\": {");
                    formParams.forEach((key, value) -> {
                        if (value != null && value.length > 0 && value[0] != null && !value[0].isEmpty()) {
                            builder.append("\"").append(key).append("\": \"").append(String.join(",", value)).append("\", ");
                        }
                    });
                    if (builder.charAt(builder.length() - 2) == ',') {
                        builder.setLength(builder.length() - 2);
                    }
                    builder.append("},\n");
                }
            }

            // 获取 multipart/form-data 中的文件字段（如果有）
            appendMultipartFiles(builder, request);

            builder.append("}");

            String paramResult;
            if (builder.length() > 10000) {
                paramResult = builder.substring(0, 10000);
            } else {
                paramResult = builder.toString();
            }

            operationLogsMapper.insert(OperationLog.builder().requestUrl(request.getRequestURI()).requestParams(paramResult)
                    .requestTime(LocalDateTime.now()).operator(SecurityContextHolder.getUserName()).clientIp(getRealIp(request))
                    .requestType(method).responseTimeMs(Math.toIntExact(duration)).responseStatus(status).errorMessage(Optional.ofNullable(ex)
                            .map(Exception::getMessage)
                            .orElse(null)).build());
        } catch (Exception e) {

            log.error("处理 GET 请求参数时发生异常: {}", e.getMessage(), e);
        }

    }

    /**
     * 获取用户真实 IP
     */
    private String getRealIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            // X-Forwarded-For 可能会包含多个 IP，取第一个非 unknown 的 IP
            return ip.split(",")[0].trim();
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_CLIENT_IP");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        return request.getRemoteAddr();
    }


    /**
     * 获取请求 Body 内容
     * @param request
     * @return
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            ServletInputStream inputStream = request.getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            // 逐步读取 InputStream 的数据
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }

            // 获取字节数组
            byte[] bodyBytes = byteArrayOutputStream.toByteArray();

            // 转换为字符串并返回
            return new String(bodyBytes, request.getCharacterEncoding());
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 处理 multipart/form-data 中的文件字段
     * @param builder
     * @param request
     * @throws IOException
     * @throws ServletException
     */
    private void appendMultipartFiles(StringBuilder builder, HttpServletRequest request) throws IOException, ServletException {
        // 获取 Content-Type
        String contentType = request.getContentType();

        // 只有 multipart/form-data 才调用 request.getParts()
        if (contentType != null && contentType.toLowerCase().startsWith("multipart/")) {
            for (String fileName : request.getParameterMap().keySet()) {
                if (request.getPart(fileName) != null) {
                    builder.append("\"").append(fileName).append("\": {");
                    builder.append("\"file_name\": \"").append(fileName).append("\", ");
                    builder.append("\"file_size\": \"").append(request.getPart(fileName).getSize()).append("\", ");
                    builder.append("\"file_path\": \"").append(request.getPart(fileName).getSubmittedFileName()).append("\"}, ");
                }
            }
        }
    }
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }
}

