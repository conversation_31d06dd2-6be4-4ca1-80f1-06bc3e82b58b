package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TaskRestoreVO implements Serializable {

    /**
     * 还原时间
     */
    @ApiModelProperty(value = "还原时间")
    private String createTime;

    /**
     * 还原id
     */
    @ApiModelProperty(value = "还原id")
    private Long id;

    /**
     * 还原状态
     */
    @ApiModelProperty(value = "还原状态")
    private Integer status;

    /**
     * 还原状态
     */
    @ApiModelProperty(value = "还原状态")
    private String statusName;

    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    private String padCode;

    /**
     * 备份id
     */
    @ApiModelProperty(value = "备份id")
    private Long backupId;

    /**
     * 备份大小
     */
    @ApiModelProperty(value = "备份大小")
    private Long backupSize;

    @ApiModelProperty(value = "备份大小说明")
    private String backupSizeDesc;

    /**
     * 备份名称
     */
    @ApiModelProperty(value = "备份名称")
    private String backupName;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 还原人
     */
    @ApiModelProperty(value = "还原人")
    private String createBy;

    /**
     * 还原方式
     */
    @ApiModelProperty(value = "还原方式")
    private String taskSource;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String errorMsg;

    private Boolean latest;
}
