<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.task.TaskMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paas.manage.model.vo.TaskVO">
        <id column="customer_task_id" property="taskId" />
        <result column="unique_id" property="taskBatchId" />
        <result column="pad_code" property="instanceId" />
        <result column="customer_id" property="customerId" />
        <result column="type" property="taskType" />
        <result column="task_content" property="taskContent" />
        <result column="task_source" property="taskSource" />
        <result column="create_time" property="createTime" />
        <result column="" property="taskName" />
        <result column="" property="appId" />
        <result column="" property="appName" />
        <result column="" property="packageName" />
        <result column="file_id" property="fileId" />
        <result column="customer_file_id" property="customerFileId" />
        <result column="" property="fileName" />
        <result column="status" property="executeStatus" />
        <result column="start_time" property="executeTime" />
        <result column="end_time" property="finishTime" />
        <result column="device_code" property="deviceCode" />
        <result column="error_msg" property="errorMsg" />
        <result column="last_image_id" property="lastImageId" />
        <result column="image_id" property="imageId" />


    </resultMap>

    <resultMap id="AllTaskResultMap" type="net.armcloud.paas.manage.model.vo.AllTaskVO">
        <id column="customer_task_id" property="taskId" />
        <result column="unique_id" property="taskBatchId" />
        <result column="pad_code" property="instanceId" />
        <result column="customer_id" property="customerId" />
        <result column="type" property="taskType" />
        <result column="task_content" property="taskContent" />
        <result column="task_source" property="taskSource" />
        <result column="create_time" property="createTime" />
        <result column="" property="taskName" />
        <result column="" property="appId" />
        <result column="" property="appName" />
        <result column="" property="packageName" />
        <result column="file_id" property="fileId" />
        <result column="customer_file_id" property="customerFileId" />
        <result column="" property="fileName" />
        <result column="status" property="executeStatus" />
        <result column="start_time" property="executeTime" />
        <result column="end_time" property="finishTime" />
        <result column="device_code" property="deviceCode" />
        <result column="error_msg" property="errorMsg" />
        <result column="last_image_id" property="lastImageId" />
        <result column="image_id" property="imageId" />
        <result column="content_json" property="contentJson" />
        <result column="id" property="masterTaskId" />

    </resultMap>

    <update id="cancelPadTask">
        update pad_task set status = -3,end_time = now(),update_time = now(),update_by = 'admin' where customer_task_id = #{taskId} and status = 1
    </update>
    <delete id="cancelRefreshMasterTaskStatus">
        update task set status = -2,update_time = now(),update_by = 'admin' where id in (
            select task_id from pad_task where customer_task_id = #{customerTaskId}
        ) and status in (1,2)
    </delete>

    <update id="cancelDeviceTask">
        update device_task set status = -3,end_time = now(),update_time = now(),update_by = 'admin' where customer_task_id = #{taskId} and status = 1
    </update>
    <delete id="cancelDeviceRefreshMasterTaskStatus">
        update task set status = -2,update_time = now(),update_by = 'admin' where id in (
            select task_id from device_task where customer_task_id = #{taskId}
        ) and status in (1,2)
    </delete>

    <select id="listTasks" resultMap="BaseResultMap">
        SELECT
            pt.customer_task_id,t.unique_id,pt.pad_code,t.customer_id,t.type,t.task_source,t.create_time,pt.status,
            pt.start_time,pt.end_time,pt.error_msg,pt.last_image_id,pt.image_id
        FROM task t
        inner join pad_task pt on t.id = pt.task_id
        <where>
            t.delete_flag = 0
            <if test="taskCode != null and taskCode != ''">
                AND pt.pad_code = #{taskCode}
            </if>
            <if test="taskId != null and taskId != ''">
                AND pt.customer_task_id = #{taskId}
            </if>
            <if test="customerId != null and customerId != ' '">
                and t.customer_id=#{customerId}
            </if>
            <if test="taskBatchId != null and taskBatchId != ''">
                AND t.unique_id = #{taskBatchId}
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="customerIds != null and customerIds.size() > 0">
                and t.customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <choose>
                <when test="taskTypes != null and taskTypes.size() > 0">
                    and t.type in
                    <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                        #{taskType}
                    </foreach>
                </when>
                <otherwise>
                    and t.type in
                    <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                        #{allTaskType}
                    </foreach>
                </otherwise>
            </choose>
            <if test="taskSources != null and taskSources.size() > 0">
                and t.task_source in
                <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                    #{taskSource}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and pt.status in
                <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                    #{statuse}
                </foreach>
            </if>
        order by t.create_time desc
        </where>
    </select>
    <select id="listApps" resultMap="BaseResultMap">
        SELECT pt.customer_task_id,
               t.unique_id,
               pt.pad_code,
               t.customer_id,
               t.type,
               t.task_source,
               t.create_time,
               pt.status,
               pt.start_time,
               pt.end_time,
               pt.file_id,
               pt.customer_file_id,
               pt.task_content,
               pt.error_msg
        FROM task t
            inner join pad_task pt on t.id = pt.task_id
        <where>
            t.delete_flag = 0
            <if test="taskCode != null and taskCode != ''">
                 AND pt.pad_code = #{taskCode}
            </if>
            <if test="taskId != null and taskId != ''">
                AND pt.customer_task_id = #{taskId}
            </if>
            <if test="customerId != null and customerId != ' '">
                and t.customer_id=#{customerId}
            </if>
            <if test="taskBatchId != null and taskBatchId != ''">
                AND t.unique_id = #{taskBatchId}
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and pt.customer_file_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="customerIds != null and customerIds.size() > 0">
                and t.customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <choose>
                <when test="taskTypes != null and taskTypes.size() > 0">
                    and t.type in
                    <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                        #{taskType}
                    </foreach>
                </when>
                <otherwise>
                    and t.type in
                    <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                        #{allTaskType}
                    </foreach>
                </otherwise>
            </choose>
            <if test="taskSources != null and taskSources.size() > 0">
                and t.task_source in
                <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                    #{taskSource}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and pt.status in
                <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                    #{statuse}
                </foreach>
            </if>
        order by t.create_time desc
        </where>
    </select>

    <select id="listDeviceTasks" resultMap="BaseResultMap">
        SELECT
        dt.customer_task_id,t.unique_id,dt.device_code,t.customer_id,t.type,t.task_source,t.create_time,dt.status,dt.start_time,
        dt.end_time,dt.file_id,dt.error_msg
        FROM task t
        inner join device_task dt on t.id = dt.task_id
        <where>
            t.delete_flag = 0
            <if test="taskCode != null and taskCode != ''">
                AND dt.device_code = #{taskCode}
            </if>
            <if test="taskId != null and taskId != ''">
                AND dt.customer_task_id = #{taskId}
            </if>
            <if test="customerId != null and customerId != ' '">
                and t.customer_id=#{customerId}
            </if>
            <if test="taskBatchId != null and taskBatchId != ''">
                AND t.unique_id = #{taskBatchId}
            </if>
            <if test="fileIds != null and fileIds.size() > 0">
                and dt.customer_file_id in
                <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
                    #{fileId}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="customerIds != null and customerIds.size() > 0">
                and t.customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <choose>
                <when test="taskTypes != null and taskTypes.size() > 0">
                    and t.type in
                    <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                        #{taskType}
                    </foreach>
                </when>
                <otherwise>
                    and t.type in
                    <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                        #{allTaskType}
                    </foreach>
                </otherwise>
            </choose>
            <if test="taskSources != null and taskSources.size() > 0">
                and t.task_source in
                <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                    #{taskSource}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and dt.status in
                <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                    #{statuse}
                </foreach>
            </if>
            order by t.create_time desc
        </where>
    </select>

    <select id="uploadListTasks" resultMap="BaseResultMap">
        SELECT
            pt.customer_task_id,t.unique_id,pt.pad_code,t.customer_id,t.type,t.task_source,t.create_time,pt.status,pt.start_time,
            pt.end_time,pt.file_id,pt.customer_file_id,pt.error_msg
        FROM task t
            inner join pad_task pt on t.id = pt.task_id
        <where>
            t.delete_flag = 0
            <if test="taskCode != null and taskCode != ''">
                AND pt.pad_code = #{taskCode}
            </if>
            <if test="taskId != null and taskId != ''">
                AND pt.customer_task_id = #{taskId}
            </if>
            <if test="customerId != null and customerId != ' '">
                and t.customer_id=#{customerId}
            </if>
            <if test="taskBatchId != null and taskBatchId != ''">
                AND t.unique_id = #{taskBatchId}
            </if>
            <if test="fileIds != null and fileIds.size() > 0">
                and pt.customer_file_id in
                <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
                    #{fileId}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="customerIds != null and customerIds.size() > 0">
                and t.customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <choose>
                <when test="taskTypes != null and taskTypes.size() > 0">
                    and t.type in
                    <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                        #{taskType}
                    </foreach>
                </when>
                <otherwise>
                    and t.type in
                    <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                        #{allTaskType}
                    </foreach>
                </otherwise>
            </choose>
            <if test="taskSources != null and taskSources.size() > 0">
                and t.task_source in
                <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                    #{taskSource}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and pt.status in
                <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                    #{statuse}
                </foreach>
            </if>
        order by t.create_time desc
        </where>

    </select>

    <select id="selectTaskByTaskTypeAndTaskStatus" resultType="java.lang.Integer">
        select count(1) from pad_task
        <where>
            delete_flag = 0
            <if test="pads != null and pads.size() > 0">
                and pad_code in
                <foreach collection="pads" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and status in
                <foreach collection="statuses" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getTaskStatistic" resultType="net.armcloud.paas.manage.model.vo.TaskStatisticVO">
        SELECT
        SUM(successNum) AS successNum,
        SUM(waitingNum) AS waitingNum,
        SUM(executingNum) AS executingNum,
        SUM(failNum) AS failNum,
        SUM(failPadNum) AS failPadNum
        FROM (
        SELECT
        COUNT(CASE WHEN pt.status = 3 THEN 1 END) AS successNum,
        COUNT(CASE WHEN pt.status = 1 THEN 1 END) AS waitingNum,
        COUNT(CASE WHEN pt.status = 2 THEN 1 END) AS executingNum,
        COUNT(CASE WHEN pt.status NOT IN (1,2,3) AND pt.error_msg NOT IN ('未安装应用', '未命中黑白名单规则') THEN 1 END) AS failNum,
        COUNT(DISTINCT CASE WHEN pt.status NOT IN (1,2,3) AND pt.error_msg NOT IN ('未安装应用', '未命中黑白名单规则') THEN pt.pad_code END) AS failPadNum
            FROM pad_task pt
            left join task t on pt.task_id = t.id
            WHERE pt.create_time >= CURDATE() and t.type not in (1040,1041,1042,1043,1044)
                AND pt.delete_flag = 0
                <if test="customerId != null">
                    AND pt.customer_id=#{customerId}
                </if>
            UNION ALL

            SELECT
                COUNT(CASE WHEN status = 3 THEN 1 END),
                COUNT(CASE WHEN status = 1 THEN 1 END),
                COUNT(CASE WHEN status = 2 THEN 1 END),
                COUNT(CASE WHEN status IN (-1, -3) THEN 1 END),
                0
            FROM fc_file_upload_tasks
            WHERE created_time >= CURDATE()
                AND delete_flag = 0
                <if test="customerId != null">
                    AND customer_id=#{customerId}
                </if>
            UNION ALL

            SELECT
                COUNT(CASE WHEN status = 3 THEN 1 END),
                COUNT(CASE WHEN status = 1 THEN 1 END),
                COUNT(CASE WHEN status = 2 THEN 1 END),
                COUNT(CASE WHEN status IN (-1, -3) THEN 1 END),
                0
            FROM device_task
            WHERE create_time >= CURDATE()
                AND delete_flag = 0
                <if test="customerId != null">
                    AND customer_id=#{customerId}
                </if>
        ) t;
    </select>

    <select id="getPadTaskStatistic" resultType="net.armcloud.paas.manage.model.vo.TaskStatisticVO">
        SELECT
            COUNT(CASE WHEN status = 3 THEN 1 END) AS successNum,
            COUNT(CASE WHEN status IN (1, 2) THEN 1 END) AS executingNum,
            COUNT(CASE WHEN status not in (1,2,3)  and error_msg !='未安装应用' and error_msg !='未命中黑白名单规则' THEN 1 END) AS failNum,
            COUNT(DISTINCT CASE WHEN status not in (1,2,3) and error_msg !='未安装应用' and error_msg !='未命中黑白名单规则' THEN pad_code END) AS failPadNum
        FROM
            pad_task
        WHERE
            create_time >= CONCAT(CURDATE(), ' 00:00:00')
          AND delete_flag = 0
        <if test="customerId != null and customerId != ''">
            and customer_id=#{customerId}
        </if>
    </select>

    <select id="getFileTaskStatistic" resultType="net.armcloud.paas.manage.model.vo.TaskStatisticVO">
        SELECT
            COUNT(CASE WHEN status = 3 THEN 1 END) AS successNum,
            COUNT(CASE WHEN status = 1 THEN 1 END) AS waitingNum,
            COUNT(CASE WHEN status = 2 THEN 1 END) AS executingNum,
            COUNT(CASE WHEN status IN (-1, -3) THEN 1 END) AS failNum
        FROM
            fc_file_upload_tasks
        WHERE
            created_time >= CONCAT(CURDATE(), ' 00:00:00')
          AND delete_flag = 0
        <if test="customerId != null and customerId != ' '">
            and customer_id=#{customerId}
        </if>
    </select>

    <select id="getDeviceTaskStatistic" resultType="net.armcloud.paas.manage.model.vo.TaskStatisticVO">
        SELECT
            COUNT(CASE WHEN status = 3 THEN 1 END) AS successNum,
            COUNT(CASE WHEN status = 1 THEN 1 END) AS waitingNum,
            COUNT(CASE WHEN status = 2 THEN 1 END) AS executingNum,
            COUNT(CASE WHEN status IN (-1, -3) THEN 1 END) AS failNum
        FROM
            device_task
        WHERE
            create_time >= CONCAT(CURDATE(), ' 00:00:00')
          AND delete_flag = 0
        <if test="customerId != null and customerId != ' '">
            and customer_id=#{customerId}
        </if>
    </select>
    <select id="successTaskStatic" resultType="net.armcloud.paas.manage.model.vo.SuccessTaskStaticVo">
        SELECT
            error_msg as taskDescription,
            TIME as time,
            type,
            sum( number) AS num
        FROM
            (
            SELECT
            a.`status`,
            b.type,
            IFNULL( a.error_msg, '' ) error_msg,
            DATE ( a.create_time ) AS TIME,
            count( 0 ) number
        FROM
            pad_task a
            INNER JOIN task b ON a.task_id = b.id
            <where>
                   b.type not in (1040,1041,1042,1043,1044)
                <if test="statusList != null and statusList.size() > 0">
                    and a.status in
                    <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
                        #{status}
                    </foreach>
                </if>
                <if test="startTime != null">
                    and a.create_time >=#{startTime}
                </if>
                <if test="endTime != null">
                    and a.create_time &lt;=#{endTime}
                </if>
                <if test="customerId != null">
                    and a.customer_id =#{customerId}
                </if>
            </where>
            GROUP BY
            a.`status`,
            type,
            TIME,
            a.error_msg
            UNION ALL
            SELECT
            a.`status`,
            b.type,
            IFNULL( a.error_msg, '' ) error_msg,
            DATE ( a.create_time ) AS TIME,
            count( 0 ) number
        FROM
            device_task a
            INNER JOIN task b ON a.task_id = b.id
            <where>
                <if test="statusList != null and statusList.size() > 0">
                    and a.status in
                    <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
                        #{status}
                    </foreach>
                </if>
                <if test="startTime != null">
                    and a.create_time >=#{startTime}
                </if>
                <if test="endTime != null">
                    and a.create_time &lt;=#{endTime}
                </if>
                <if test="customerId != null">
                    and a.customer_id =#{customerId}
                </if>
            </where>
            GROUP BY
            a.`status`,
            type,
            TIME,
            a.error_msg UNION ALL
            SELECT
            a.`status`,
            b.type,
            IFNULL( a.error_msg, '' ) error_msg,
            DATE ( a.create_time ) AS TIME,
            count( 0 ) number
        FROM
            file_task a
            INNER JOIN task b ON a.task_id = b.id
            <where>
                <if test="statusList != null and statusList.size() > 0">
                    and a.status in
                    <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
                        #{status}
                    </foreach>
                </if>
                <if test="startTime != null">
                    and a.create_time >=#{startTime}
                </if>
                <if test="endTime != null">
                    and a.create_time &lt;=#{endTime}
                </if>
                <if test="customerId != null">
                    and a.customer_id =#{customerId}
                </if>
            </where>
            GROUP BY
            a.`status`,
            TIME,
            type,
            a.error_msg
            ) a
        GROUP BY
            TIME,
            type,
            error_msg
        order by num desc

    </select>
    <select id="failTaskStatic" resultType="net.armcloud.paas.manage.model.vo.SuccessTaskStaticVo">
        SELECT
        error_msg as taskDescription,
        TIME as time,
        type,
        sum( number) AS num
        FROM
        (
        SELECT
        a.`status`,
        b.type,
        IFNULL( a.error_msg, '' ) error_msg,
        DATE ( a.create_time ) AS TIME,
        count( 0 ) number
        FROM
        pad_task a
        INNER JOIN task b ON a.task_id = b.id
        where a.error_msg != '未安装应用' and a.error_msg != '未命中黑白名单规则'
            <if test="statusList != null and statusList.size() > 0">
                and a.status not in
                <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="startTime != null">
                and a.create_time >=#{startTime}
            </if>
            <if test="endTime != null">
                and a.create_time &lt;=#{endTime}
            </if>
            <if test="customerId != null">
                and a.customer_id =#{customerId}
            </if>
        GROUP BY
        a.`status`,
        type,
        TIME,
        a.error_msg
        UNION ALL
        SELECT
        a.`status`,
        b.type,
        IFNULL( a.error_msg, '' ) error_msg,
        DATE ( a.create_time ) AS TIME,
        count( 0 ) number
        FROM
        device_task a
        INNER JOIN task b ON a.task_id = b.id
        <where>
            <if test="statusList != null and statusList.size() > 0">
                and a.status not in
                <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="startTime != null">
                and a.create_time >=#{startTime}
            </if>
            <if test="endTime != null">
                and a.create_time &lt;=#{endTime}
            </if>
            <if test="customerId != null">
                and a.customer_id =#{customerId}
            </if>
        </where>
        GROUP BY
        a.`status`,
        type,
        TIME,
        a.error_msg UNION ALL
        SELECT
        a.`status`,
        b.type,
        IFNULL( a.error_msg, '' ) error_msg,
        DATE ( a.create_time ) AS TIME,
        count( 0 ) number
        FROM
        file_task a
        INNER JOIN task b ON a.task_id = b.id
        <where>
            <if test="statusList != null and statusList.size() > 0">
                and a.status not in
                <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="startTime != null">
                and a.create_time >=#{startTime}
            </if>
            <if test="endTime != null">
                and a.create_time &lt;=#{endTime}
            </if>
            <if test="customerId != null">
                and a.customer_id =#{customerId}
            </if>
        </where>
        GROUP BY
        a.`status`,
        TIME,
        type,
        a.error_msg
        ) a
        GROUP BY
        TIME,
        type,
        error_msg
        order by num desc

    </select>
    <select id="failPadCodeList" resultType="net.armcloud.paas.manage.model.vo.FailPadDetailVo">
        SELECT * FROM (
        SELECT
        count(0) as num,
        pad_code as padCode,
        DATE ( a.create_time ) AS TIME
        FROM
        pad_task a
        INNER JOIN task b ON a.task_id = b.id
        where a.error_msg != '未安装应用' and a.error_msg != '未命中黑白名单规则' and a.status not in ( 1,2,3 )
        <if test="startTime != null">
            and a.create_time >=#{startTime}
        </if>
        <if test="endTime != null">
            and a.create_time &lt;=#{endTime}
        </if>
        <if test="customerId != null">
            and a.customer_id =#{customerId}
        </if>
        GROUP BY
        a.pad_code,TIME
        )a order by num desc
    </select>

    <select id="selectDeviceCodeById" resultType="net.armcloud.paas.manage.model.vo.PullModeDeviceTaskVO">
        select pt.id,pt.device_code,pt.status,pt.task_id,t.type from device_task pt
        left join task t on t.id = pt.task_id
        where pt.customer_task_id = #{customerTaskId} and pt.status = 1 limit 1;
    </select>

    <select id="existDeviceTaskByStatus" resultType="java.lang.Long">
        SELECT
        dt.id
        FROM
        device_task dt
        LEFT JOIN task t ON dt.task_id = t.id
        WHERE
        dt.device_code = #{deviceCode}
        AND dt.delete_flag = 0
        AND t.type IN
        <foreach collection="typeList" item="type" open=" (" close=")" separator=",">
            #{type}
        </foreach>
        AND dt.status IN
        <foreach collection="statusList" item="status" open=" (" close=")" separator=",">
            #{status}
        </foreach>
        limit 1
    </select>

    <select id="allListTasks" resultMap="AllTaskResultMap">
        SELECT * FROM (
        SELECT
        t.id,
        dt.id as subTaskId,
        dt.customer_task_id,
        t.unique_id,
        dt.device_code,
        NULL AS pad_code,
        t.customer_id,
        t.type,
        t.task_source,
        t.create_time,
        dt.STATUS AS status,
        dt.start_time AS start_time,
        dt.end_time AS end_time,
        dt.file_id,
        NULL AS customer_file_id,
        dt.error_msg AS error_msg,
        NULL AS last_image_id,
        NULL AS image_id,
        NULL AS task_content
        FROM task t
        JOIN device_task dt ON t.id = dt.task_id
        <where>
            t.delete_flag = 0
            <if test="idNo != null and idNo != ''">
                <if test="idNoType != null and idNoType == 1">
                    and dt.device_code = #{idNo}
                </if>
                <if test="idNoType != null and idNoType == 2">
                    and 1 != 1
                </if>
                <if test="idNoType != null and idNoType == 3">
                    and dt.customer_task_id = #{idNo}
                </if>
                <if test="idNoType != null and idNoType == 4">
                    and 1 != 1
                </if>
                <if test="idNoType != null and idNoType == 5">
                    and 1 != 1
                </if>
            </if>


            <if test="customerId != null and customerId != ''">
                and t.customer_id=#{customerId}
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="customerIds != null and customerIds.size() > 0">
                and t.customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <if test="taskTypes != null and taskTypes.size() > 0">
                and t.type in
                <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                    #{taskType}
                </foreach>
            </if>
            <if test="allTaskTypes != null and allTaskTypes.size() > 0">
                and t.type in
                <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                    #{allTaskType}
                </foreach>
            </if>
            <if test="taskSources != null and taskSources.size() > 0">
                and t.task_source in
                <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                    #{taskSource}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and dt.status in
                <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                    #{statuse}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and 1 != 1
            </if>
            <if test="fileIds != null and fileIds.size() > 0">
                and 1 != 1
            </if>
        </where>

        UNION ALL

        SELECT
        t.id,
        pt.id as subTaskId,
        pt.customer_task_id,
        t.unique_id,
        NULL AS device_code,
        pt.pad_code AS pad_code,
        t.customer_id,
        t.type,
        t.task_source,
        t.create_time,
        pt.STATUS AS status,
        pt.start_time AS start_time,
        pt.end_time AS end_time,
        NULL AS file_id,
        pt.customer_file_id,
        pt.error_msg AS error_msg,
        pt.last_image_id,
        pt.image_id,
        pt.task_content
        FROM task t
        JOIN pad_task pt ON t.id = pt.task_id
        <where>
            t.delete_flag = 0
            <if test="idNo != null and idNo != ''">
                <if test="idNoType != null and idNoType == 1">
                    <if test="padCodes != null and padCodes.size() > 0">
                        and pt.pad_code in
                        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                            #{padCode}
                        </foreach>
                    </if>
                    <if test="padCodes == null or padCodes.size() == 0">
                        and 1 != 1
                    </if>
                </if>
                <if test="idNoType != null and idNoType == 2">
                    and pt.pad_code = #{idNo}
                </if>
                <if test="idNoType != null and idNoType == 3">
                    and pt.customer_task_id = #{idNo}
                </if>
                <if test="idNoType != null and idNoType == 4">

                </if>
                <if test="idNoType != null and idNoType == 5">

                </if>
            </if>


            <if test="customerId != null and customerId != ''">
                and t.customer_id=#{customerId}
            </if>
            <if test="createTimeStart != null and createTimeStart !=''">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="customerIds != null and customerIds.size() > 0">
                and t.customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
            <if test="taskTypes != null and taskTypes.size() > 0">
                and t.type in
                <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                    #{taskType}
                </foreach>
            </if>
            <if test="allTaskTypes != null and allTaskTypes.size() > 0">
                and t.type in
                <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                    #{allTaskType}
                </foreach>
            </if>
            <if test="taskSources != null and taskSources.size() > 0">
                and t.task_source in
                <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                    #{taskSource}
                </foreach>
            </if>
            <if test="statuses != null and statuses.size() > 0">
                and pt.status in
                <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                    #{statuse}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and pt.customer_file_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="fileIds != null and fileIds.size() > 0">
                and pt.customer_file_id in
                <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
                    #{fileId}
                </foreach>
            </if>
        </where>
        ) AS combined_results
        ORDER BY create_time DESC, id DESC
    </select>

    <select id="allListTasks2" resultMap="AllTaskResultMap">
        SELECT
        t.id,
        CASE
        WHEN dt.id IS NOT NULL THEN dt.id
        ELSE pt.id
        END AS sub_task_id,
        CASE
        WHEN dt.task_id IS NOT NULL THEN dt.customer_task_id
        ELSE pt.customer_task_id
        END AS customer_task_id,
        t.unique_id,
        dt.device_code,
        pt.pad_code,
        t.customer_id,
        t.type,
        t.task_source,
        t.create_time,
        CASE
        WHEN dt.task_id IS NOT NULL THEN dt.status
        ELSE pt.status
        END AS status,
        CASE
        WHEN dt.task_id IS NOT NULL THEN dt.start_time
        ELSE pt.start_time
        END AS start_time,
        CASE
        WHEN dt.task_id IS NOT NULL THEN dt.end_time
        ELSE pt.end_time
        END AS end_time,
        dt.file_id,
        pt.customer_file_id,
        CASE
        WHEN dt.task_id IS NOT NULL THEN dt.error_msg
        ELSE pt.error_msg
        END AS error_msg,
        pt.last_image_id,
        pt.image_id,
        pt.task_content
        FROM task t
        LEFT JOIN device_task dt ON t.id = dt.task_id
        LEFT JOIN pad_task pt ON t.id = pt.task_id
        WHERE t.delete_flag = 0
        <if test="idNo != null and idNo != ''">
            <if test="idNoType != null and idNoType == 1">
            AND( dt.device_code = #{idNo}
                <if test="padCodes != null and padCodes.size() > 0">
                    or pt.pad_code in
                    <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                        #{padCode}
                    </foreach>
                </if>
                )
            </if>
            <if test="idNoType != null and idNoType == 2">
                and pt.pad_code = #{idNo}
            </if>
            <if test="idNoType != null and idNoType == 3">
                and (pt.customer_task_id = #{idNo} or dt.customer_task_id = #{idNo})
            </if>
        </if>
        <if test="statuses != null and statuses.size() > 0">
            AND (dt.status IN
            <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                #{statuse}
            </foreach>
            or
            pt.status IN
            <foreach collection="statuses" item="statuse" open="(" separator="," close=")">
                #{statuse}
            </foreach>)
        </if>
        <if test="appIds != null and appIds.size() > 0">
            AND pt.customer_file_id IN
            <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                #{appId}
            </foreach>
        </if>
        <if test="fileIds != null and fileIds.size() > 0">
            AND pt.customer_file_id IN
            <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
                #{fileId}
            </foreach>
        </if>
        <if test="customerId != null and customerId != ''">
            AND t.customer_id = #{customerId}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            AND t.create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            AND #{createTimeEnd} >= t.create_time
        </if>
        <if test="customerIds != null and customerIds.size() > 0">
            AND t.customer_id IN
            <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </if>
        <if test="taskTypes != null and taskTypes.size() > 0">
            AND t.type IN
            <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                #{taskType}
            </foreach>
        </if>
        <if test="allTaskTypes != null and allTaskTypes.size() > 0">
            AND t.type IN
            <foreach collection="allTaskTypes" item="allTaskType" open="(" separator="," close=")">
                #{allTaskType}
            </foreach>
        </if>
        <if test="taskSources != null and taskSources.size() > 0">
            AND t.task_source IN
            <foreach collection="taskSources" item="taskSource" open="(" separator="," close=")">
                #{taskSource}
            </foreach>
        </if>
        AND (dt.task_id IS NOT NULL OR pt.task_id IS NOT NULL)
        ORDER BY t.create_time DESC, t.id DESC
    </select>

    <select id="batchSelectList" resultType="net.armcloud.paascenter.common.model.entity.task.TaskQueue">
        select `key`,master_task_id,content_json from task_queue
        where master_task_id in
        <foreach collection="masterTaskIdList" item="masterTaskId" open="(" separator="," close=")">
            #{masterTaskId}
        </foreach>
         and `key` in
        <foreach collection="keyList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="getFirstIdFromToday" resultType="java.lang.Long">
        SELECT id FROM pad_task
        WHERE create_time >=CURDATE()
        order by create_time asc
            LIMIT 1
    </select>

    <select id="getMaxId" resultType="java.lang.Long">
        SELECT MAX(id) FROM pad_task;
    </select>


    <select id="getPadTaskStatisticByIdRange" resultType="net.armcloud.paas.manage.model.vo.TaskStatisticVO">
        SELECT
        COUNT(CASE WHEN pt.status = 1 THEN 1 END) AS waitingNum,
        COUNT(CASE WHEN pt.status = 3 THEN 1 END) AS successNum,
        COUNT(CASE WHEN pt.status = 2 THEN 1 END) AS executingNum,
        COUNT(CASE WHEN pt.status not in (1,2,3) AND error_msg != '未安装应用' AND error_msg != '未命中黑白名单规则' THEN 1 END) AS failNum,
        COUNT(DISTINCT CASE WHEN pt.status not in (1,2,3) AND error_msg != '未安装应用' AND error_msg != '未命中黑白名单规则' THEN pad_code END) AS failPadNum
        FROM pad_task pt
        left join task t on pt.task_id = t.id
        WHERE pt.id BETWEEN #{startId} AND #{endId} and t.type not in (1040,1041,1042,1043,1044)
        <if test="customerId != null">
            AND pt.customer_id=#{customerId}
        </if>
        AND pt.delete_flag = 0
    </select>




</mapper>