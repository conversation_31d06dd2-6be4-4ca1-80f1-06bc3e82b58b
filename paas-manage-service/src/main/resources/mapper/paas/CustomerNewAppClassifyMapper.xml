<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerNewAppClassifyMapper">

    <insert id="cusInsert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.manage.CustomerNewAppClassify" useGeneratedKeys="true">
        INSERT INTO customer_new_app_classify ( customer_id, classify_name,enable, remark, create_by,create_time,update_by,update_time)
        VALUES (#{customerId},#{classifyName},#{enable},#{remark},#{createBy},#{createTime},#{updateBy},#{updateTime})
    </insert>

    <select id="selectClassifyList" resultType="net.armcloud.paas.manage.model.vo.NewAppClassifyVO">
        select cnac.id, cnac.customer_id, cnac.classify_name, cnac.enable, cnac.remark, cnac.create_by, cnac.create_time, cnac.update_by, cnac.update_time, (select count(1) from customer_new_app_classify_relation cnacr where cnacr.new_app_classify_id = cnac.id) as app_num,cr.customer_name from customer_new_app_classify cnac
        left join customer cr on cnac.customer_id = cr.id
        <where>
            <if test="customerId != null">
                and cnac.customer_id = #{customerId}
            </if>
            <if test="classifyName != null and classifyName != ''">
                and cnac.classify_name like CONCAT('%',#{classifyName},'%')
            </if>
            <if test="enable != null">
                and cnac.enable = #{enable}
            </if>
        </where>
        order by cnac.id desc
    </select>
</mapper>
