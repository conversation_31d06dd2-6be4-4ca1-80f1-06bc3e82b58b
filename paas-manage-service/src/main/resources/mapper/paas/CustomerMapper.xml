<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.Customer">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="associates" jdbcType="VARCHAR" property="associates" />
        <result column="customer_account" jdbcType="VARCHAR" property="customerAccount" />
        <result column="customer_tel" jdbcType="BIGINT" property="customerTel" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <resultMap id="AccessCustomer" type="net.armcloud.paas.manage.model.vo.CustomerVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="associates" jdbcType="VARCHAR" property="associates" />
        <result column="customer_account" jdbcType="VARCHAR" property="customerAccount" />
        <result column="customer_tel" jdbcType="BIGINT" property="customerTel" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="access_key_id" jdbcType="VARCHAR" property="accessKeyId" />
        <result column="secret_access_key" jdbcType="TIMESTAMP" property="secretAccessKey" />
        <result column="dict_label" jdbcType="VARCHAR" property="statusName" />
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
        <result column="role_names" property="roleNames"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, customer_name,password, associates, customer_account, customer_tel, `status`, create_by,
        create_time, update_by, update_time,customer_code
    </sql>
    <sql id="AccessCustomer_Column_List">
        c.id,
        c.customer_name,
        c.password,
        c.associates,
        c.customer_account,
        c.customer_tel,
        c.status,
        ANY_VALUE(ca.access_key_id) AS access_key_id,  <!-- 聚合 -->
        ANY_VALUE(ca.secret_access_key) AS secret_access_key,  <!-- 聚合 -->
        ANY_VALUE(d.dict_label) AS dict_label,  <!-- 聚合 -->
        GROUP_CONCAT(DISTINCT sr.role_name ORDER BY sr.role_name SEPARATOR ',') AS role_names
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update customer set status = 0 where id = #{id}
    </delete>

    <select id="selectPageList" resultMap="AccessCustomer">
        SELECT
        <include refid="AccessCustomer_Column_List"/>
        FROM customer c
        LEFT JOIN sys_user_role sur ON sur.user_id = c.id
        LEFT JOIN sys_role sr ON sr.role_id = sur.role_id
        LEFT JOIN customer_access ca ON c.id = ca.customer_id
        LEFT JOIN dict d ON d.dict_value = c.`status` AND d.dict_type = 'customer_state'
        WHERE
        c.id != 0
        <if test="id ==0 or id != null and id != ''">
            AND c.id = #{id}
        </if>
        <if test="customerCode != null and customerCode != ''">
            AND c.customer_code = #{customerCode}
        </if>
        <if test="customerName != null and customerName != ''">
            AND c.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="associates != null and associates != ''">
            AND c.associates = #{associates}
        </if>
        <if test="customerAccount != null and customerAccount != ''">
            AND c.customer_account LIKE CONCAT('%', #{customerAccount}, '%')
        </if>
        <if test="customerTel != null and customerTel != ''">
            AND c.customer_tel LIKE CONCAT('%', #{customerTel}, '%')
        </if>
        <if test="status != null">
            AND c.`status` = #{status}
        </if>
        <if test="roleIds != null and roleIds.length > 0">
            AND sr.role_id IN
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        GROUP BY
        c.id
        ORDER BY
        c.create_time DESC
    </select>
    <select id="selectByPrimaryKey" resultMap="AccessCustomer">
        select
            customer.id, customer.customer_name,customer.password ,customer.associates, customer.customer_account,
            customer.customer_tel, customer.status, customer_access.access_key_id,customer_access.secret_access_key
                ,customer.customer_code
        from customer,customer_access
        where
        customer.id = customer_access.customer_id
        and customer.id = #{id}
    </select>
    <select id="selectByIdAndAccount" resultType="java.lang.Long">
        select id
        from customer
        <where>
            status = 1
            <if test="id != null and id != ''">
               AND id = #{id}
            </if>
            <if test="customerAccount != null and customerAccount != ''">
               AND customer_account like CONCAT('%',#{customerAccount},'%')
            </if>
            <if test="customerCode != null and customerCode != ''">
                AND customer_code = #{customerCode}
            </if>
        </where>
    </select>
    <select id="selectByNameAndPassword" resultMap="AccessCustomer">
        select
        <include refid="Base_Column_List"/>
        from customer
        where customer_account = #{username}
        and password = #{password}
        and status = 1
    </select>
    <select id="selectByTelAndAccount" resultType="java.lang.Boolean">
        select count(1)
        from customer
        where status = 1
          <if test="customerTel != null and customerTel !=''">
              and customer_tel = #{customerTel}
          </if>
            <if test="customerAccount != null and customerAccount !=''">
                and customer_account = #{customerAccount}
            </if>
    </select>
    <select id="selectById" resultType="net.armcloud.paascenter.common.model.entity.paas.Customer">
        select
        <include refid="Base_Column_List"/>
        from customer
        where id = #{id}
        and status = 1
    </select>
    <select id="selectByIds" resultType="net.armcloud.paascenter.common.model.entity.paas.Customer">
        select
        <include refid="Base_Column_List"/>
        from customer
        where id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getCustomerSelection" resultType="net.armcloud.paas.manage.model.vo.CustomerSelectionVO">
        select
            id,
            customer_name
        from customer
        where status = 1
    </select>
    <select id="selectionListCustomer" resultType="net.armcloud.paas.manage.model.vo.CustomerVO">
        select
        id, customer_name,customer_code,customer_account as customerAccount
        from customer
        where status = 1 and id != 0
        <if test="customerId != null and customerId != '' ">
            and id = #{customerId}
        </if>
        <if test="customerName != null and customerName != '' ">
            and customer.customer_name like CONCAT('%',#{customerName},'%')
        </if>
        <if test="customerAccount != null and customerAccount != '' ">
            and customer.customer_account like CONCAT('%',#{customerAccount},'%')
        </if>
    </select>
    <select id="selectByTelAndName" resultType="java.lang.Boolean">
        select count(1)
        from customer
        where status = 1
        <if test="customerName != null and customerName !=''">
            and customer_name = #{customerName}
        </if>
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.Customer" useGeneratedKeys="true">
        insert into customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerCode != null">
                customer_code,
            </if>
            <if test="customerName != null">
                customer_name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="associates != null">
                associates,
            </if>
            <if test="customerAccount != null">
                customer_account,
            </if>
            <if test="customerTel != null">
                customer_tel,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerCode != null">
                #{customerCode,jdbcType=BIGINT},
            </if>
            <if test="customerName != null">
                #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="associates != null">
                #{associates,jdbcType=VARCHAR},
            </if>
            <if test="customerAccount != null">
                #{customerAccount,jdbcType=VARCHAR},
            </if>
            <if test="customerTel != null">
                #{customerTel,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertZero">
        insert into customer (id, customer_name, customer_code, password, associates, customer_account, customer_tel,
                              status, create_by, create_time, update_by, update_time)
        VALUES (#{id},
                #{customerName},
                #{customerCode},
                #{password},
                #{associates},
                #{customerAccount},
                #{customerTel},
                #{status},
                #{createBy},
                #{createTime},
                #{updateBy},
                #{updateTime})
    </insert>

    <update id="updateByPrimaryKey" parameterType="net.armcloud.paascenter.common.model.entity.paas.Customer">
        update customer
        <set>
            <if test="customerName != null">
                customer_name = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="associates != null">
                associates = #{associates,jdbcType=VARCHAR},
            </if>
            <if test="customerAccount != null">
                customer_account = #{customerAccount,jdbcType=VARCHAR},
            </if>
            customer_tel = #{customerTel,jdbcType=BIGINT},
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="enableByPrimaryKey">
        update customer set status = 1 where id = #{id}
    </update>
    <update id="updateZero">
        update customer set id=0 where id=#{id}
    </update>

    <select id="selectBatchByIds" resultMap="AccessCustomer">
        select
        id, customer_name,password ,associates, customer_account,
        customer_tel, status,customer_code
        from customer
        where
        id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>