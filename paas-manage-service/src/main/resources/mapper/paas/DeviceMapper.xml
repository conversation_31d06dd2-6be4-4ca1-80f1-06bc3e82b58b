<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.DeviceMapper">
    <resultMap id="deviceResultMap" type="net.armcloud.paas.manage.model.vo.DeviceVO">
        <result column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="device_level" property="instanceType" />
        <result column="cloud_vendor_type" property="cloudVendorType" />
        <result column="cluster_code" property="clusterCode" />
        <result column="dc_code" property="dcCode" />
        <result column="device_out_code" property="deviceOutCode" />
        <result column="customer_account" property="customerAccount" />
        <result column="customer_id" property="customerId" />
        <result column="customer_code" property="customerCode" />
        <result column="start_time" property="assignTime" />
        <result column="expiration_time" property="expireTime" />
        <result column="device_status" property="cloudStatus" />
        <result column="device_ip" property="deviceIp" />
        <result column="recovery_time" property="recoveryTime" />
        <result column="debian_sys_info" property="debianSysInfo" />
        <result column="debian_boot_info" property="debianBootInfo" />
        <result column="ext_life_time_info" property="extLifeTimeInfo" />
        <result column="cbs_info" property="cbsInfo" />
        <result column="arm_ip" property="armServerIp" />
    </resultMap>
    <update id="deleteBatchByDevices">
        update device
        set delete_flag = 1
        where id in
        <foreach collection="deviceVOS" item="deviceVO" open="(" separator="," close=")">
            #{deviceVO.id}
        </foreach>
    </update>
    <delete id="deleteByDeviceIds">
        delete from device
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryDeviceList" resultMap="deviceResultMap">
        SELECT  d.device_code,
                d.device_level,
                d.cloud_vendor_type,
                dc.dict_label AS cloudVendorTypeName,
                di.cluster_code,
                d.device_out_code,
                c.customer_account,
                c.id AS customer_id,
                c.customer_code as customerCode,
                cd.start_time,
                cd.expiration_time,
                d.device_status,
                dc1.dict_label AS cloudStatusName,
                d.id,
                d.device_ip,
                cd.recovery_time,
                d.mac_address as macAddress,
                d.soc_model as socModel,
                d.arm_server_code as armServerCode,
                d.pad_allocation_status as padAllocationStatus,
                dc2.dict_label AS padAllocationStatusName,
                arm.online as armServerOnline,
                dc3.dict_label as armServerOnlineName,
                dci.dc_code,
                d.cbs_info,
                d.debian_boot_info,
                d.debian_sys_info,
                d.gateway,
                d.ext_life_time_info,
                arm.arm_ip
        FROM device as d

            LEFT JOIN customer_device AS cd ON d.id = cd.device_id AND cd.delete_flag=0
            LEFT JOIN customer AS c ON c.id=cd.customer_id
            left join arm_server as arm on arm.arm_server_code = d.arm_server_code and arm.delete_flag=0
            LEFT JOIN edge_cluster AS di ON di.cluster_code = arm.cluster_code and di.delete_flag=0
            LEFT JOIN dc_info as dci ON dci.idc = d.idc and dci.delete_flag = 0
            left JOIN dict AS dc on dc.dict_value = d.cloud_vendor_type and dc.dict_type='resource_supplier'
            left JOIN dict AS dc1 on dc1.dict_value = d.device_status and dc1.dict_type='resource_cloud_machine_status'
            left JOIN dict AS dc2 on dc2.dict_value = d.pad_allocation_status and dc2.dict_type='pad_allocation_status'
            left join dict as dc3 on dc3.dict_value = arm.online and dc3.dict_type='operation_server_online_status'
        WHERE
            d.delete_flag=0 and init_status in (0,1)
        <if test="armServerIp != null and armServerIp != ''">
            and arm.arm_ip like CONCAT('%',#{armServerIp},'%')
        </if>
        <if test="armServerIpList != null and armServerIpList.size() > 0">
            and arm.arm_ip in
            <foreach collection="armServerIpList" item="armServerIp" open="(" separator="," close=")">
                #{armServerIp}
            </foreach>
        </if>
        <if test="deviceCode != null and deviceCode != ''">
            and d.device_code like CONCAT('%',#{deviceCode},'%')
        </if>
        <if test="deviceCodeList != null and deviceCodeList.size() > 0">
            and d.device_code in
            <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
                #{deviceCode}
            </foreach>
        </if>
        <if test="deviceIp != null and deviceIp != ''">
            and d.device_ip like CONCAT('%',#{deviceIp},'%')
        </if>
        <if test="deviceIpList != null and deviceIpList.size() > 0">
            and d.device_ip in
            <foreach collection="deviceIpList" item="deviceIp" open="(" separator="," close=")">
                #{deviceIp}
            </foreach>
        </if>
        <if test="debianSysInfo != null and debianSysInfo != '' ">
            and d.debian_sys_info = #{debianSysInfo}
        </if>
        <if test="debianBootInfo != null and debianBootInfo != '' ">
            and d.debian_boot_info = #{debianBootInfo}
        </if>
        <if test="cbsInfo != null and cbsInfo != '' ">
            and d.cbs_info = #{cbsInfo}
        </if>
        <if test="netStorageResFlag != null">
            and d.net_storage_res_flag = #{netStorageResFlag}
        </if>
        <if test="armServerCode != null and armServerCode != ''">
            and d.arm_server_code like CONCAT('%',#{armServerCode},'%')
        </if>
        <if test="customerAccount != null and customerAccount != ''">
            and c.customer_account like CONCAT('%',#{customerAccount},'%')
        </if>
        <if test="customerId==0 or customerId != null and customerId != ''">
            and c.id=#{customerId}
        </if>
        <if test="customerCode != null and customerCode != '' ">
            and c.customer_code = #{customerCode}
        </if>
        <if test="customerId != null and customerId != '' ">
            and cd.customer_id = #{customerId}
        </if>
        <if test="instanceTypes != null and instanceTypes.size() > 0">
            and d.device_level in
            <foreach collection="instanceTypes" item="instanceType" open="(" separator="," close=")">
                #{instanceType}
            </foreach>
        </if>
        <if test="socModel != null and socModel != ''">
            and d.soc_model=#{socModel}
        </if>
        <if test="padAllocationStatus != null and padAllocationStatus != 10">
            and d.pad_allocation_status=#{padAllocationStatus}
        </if>
        <if test="padAllocationStatus != null and padAllocationStatus == 10">
            and exists (select dp.device_id from device_pad dp
            left join pad p on d.id = dp.device_id and dp.pad_id = p.id
            where p.status =1 and p.online = 0 limit 1)
        </if>
        <if test="suppliers != null and suppliers.size() > 0">
            and d.cloud_vendor_type in
            <foreach collection="suppliers" item="supplier" open="(" separator="," close=")">
                #{supplier}
            </foreach>
        </if>
<!--        <if test="idcIntegers != null and idcIntegers.size() > 0">-->
<!--            and di.id in-->
<!--            <foreach collection="idcIntegers" item="idc" open="(" separator="," close=")">-->
<!--                #{idc}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="stIntegers != null and stIntegers != ''">
            <choose>
                <when test="stIntegers == 1">
                    and cd.id is null
                </when>
                <when test="stIntegers == 2">
                   and cd.id is not null
                </when>
                <when test="stIntegers == 3">
                    and d.device_status = 2
                </when>
            </choose>
        </if>
        <if test="cloudtIntegers != null and cloudtIntegers.size() > 0">
            and d.device_status in
            <foreach collection="cloudtIntegers" item="cloudtInteger" open="(" separator="," close=")">
                #{cloudtInteger}
            </foreach>
        </if>
        order by d.device_code asc
    </select>

    <select id="selectDeviceInfoByDeviceCode" resultType="net.armcloud.paas.manage.model.vo.DeviceInfoVo">
        select
        t1.device_ip as deviceIp,t2.dc_code as dcCode
        from device t1
        join dc_info t2 on t2.id = t1.dc_id
        left join customer_device_record t3 on t1.id = t3.device_id
        where
        t1.delete_flag = 0 and
            t1.device_code in
        <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
            #{deviceCode}
        </foreach>
        <if test="customerId != null">
            and t3.customer_id = #{customerId}
        </if>
    </select>

    <select id="selectionListDevice" resultType="net.armcloud.paas.manage.model.vo.SelectionListDeviceVo">

        select
            t1.device_ip as deviceIp,t1.device_code as deviceCode
        from device t1
        LEFT JOIN customer_device cd on t1.id = cd.device_id
        LEFT JOIN arm_server t2  on t2.arm_server_code = t1.arm_server_code
        where device_status = 1
        and pad_allocation_status in (0 , -1)
        and t1.delete_flag = 0
        and t2.delete_flag = 0
        and t2.status = 1
        <if test="clusterCode != null and clusterCode != ''">
          and t2.cluster_code = #{clusterCode}
        </if>
        <if test="customerId != null and customerId != ''">
            and cd.customer_id = #{customerId}
            and cd.delete_flag = 0
        </if>
        <if test="armServerCodes != null and armServerCodes.size() > 0">
            and t1.arm_server_code in
            <foreach collection="armServerCodes" item="armServerCode" open="(" separator="," close=")">
                #{armServerCode}
            </foreach>
        </if>
        GROUP BY t1.device_ip, t1.device_code, t1.id
        order by t1.id asc
        <if test="number != null">
            limit #{number}
        </if>
    </select>
    <select id="selectDeviceByArmServerCode" resultType="net.armcloud.paas.manage.model.vo.DeviceVO">
        select distinct device_ip as deviceIp,device_status as status,pad_allocation_status as padAllocationStatus,device_code as deviceCode
        from device
        where arm_server_code = #{armServerCode}
            and delete_flag = 0
    </select>
    <select id="padAllocationDeviceList" resultType="java.lang.String">
        select distinct device_ip
        from device
        where device_ip in
        <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
            #{deviceIp}
        </foreach>
        and (pad_allocation_status not in (0,-1) or device_status = 0)
        and delete_flag = 0
    </select>

    <select id="getDeviceInfo" resultType="net.armcloud.paas.manage.model.vo.DeviceVO">
        select distinct a.device_ip as deviceIp, a.device_status as status, a.pad_allocation_status as padAllocationStatus, a.device_code as deviceCode, b.customer_id as customerId
        from device a
        left join customer_device b on a.id = b.device_id and b.delete_flag = 0
        where a.device_ip in
        <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
            #{deviceIp}
        </foreach>
        and a.delete_flag = 0
    </select>
    <select id="selectDeviceByArmServerCodeByInitStatus" resultType="net.armcloud.paas.manage.model.vo.ArmServerOnlineVO">
        SELECT count(init_status) initStatusCount,init_status initStatus from device where arm_server_code = #{armServerCode} and delete_flag = 0 GROUP BY init_status
    </select>
    <select id="selectDeviceIpIsNull" resultType="net.armcloud.paas.manage.model.vo.DeviceVO">
        select distinct device_ip as deviceIp,device_status as status,pad_allocation_status as padAllocationStatus
        from device
        where arm_server_code = #{armServerCode}
          AND (device_ip IS NULL OR device_ip = '');
    </select>
    <select id="selectBatchById" resultType="net.armcloud.paas.manage.model.vo.DeviceVO">
        SELECT
        d.id as id,
        cd.customer_id as customerId
        FROM customer_device cd
        left join device d on d.id=cd.device_id and cd.delete_flag=0
        WHERE d.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
        </foreach>
    </select>
    <select id="selectByIp" resultType="java.lang.Integer">
        select count(1) from device where device_ip like concat(#{deviceIp},'%') and delete_flag = 0
    </select>
    <select id="selectByDeviceCode" resultType="net.armcloud.paascenter.common.model.entity.paas.Device">
        select
            d.id, d.dc_id, d.device_level, d.device_code, d.device_out_code, d.cloud_vendor_type, d.device_status, d.pad_allocation_status,
        d.device_ip, d.idc, d.create_by, d.create_time, d.update_by, d.update_time, d.arm_server_code, d.debian_sys_info, d.debian_boot_info,arm.cluster_code
        from device d
        left join arm_server arm on arm.arm_server_code = d.arm_server_code
        where d.device_code in
            <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
                #{deviceCode}
            </foreach>
    </select>

    <insert id="saveDevice" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.Device" useGeneratedKeys="true">
        insert into device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dcId != null">
                dc_id,
            </if>
            <if test="deviceLevel != null">
                device_level,
            </if>
            <if test="deviceCode != null">
                device_code,
            </if>
            <if test="deviceOutCode != null">
                device_out_code,
            </if>
            <if test="cloudVendorType != null">
                cloud_vendor_type,
            </if>
            <if test="deviceStatus != null">
                device_status,
            </if>
            <if test="deviceIp != null">
                device_ip,
            </if>
            <if test="idc != null">
                idc,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="armServerCode != null">
                arm_server_code,
            </if>

            <if test="socModel != null">
                soc_model,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="padAllocationStatus != null">
                pad_allocation_status,
            </if>
            <if test="macAddress != null">
                mac_address,
            </if>
            <if test="initStatus != null">
                init_status,
            </if>
            <if test="debianSysInfo != null">
                debian_sys_info,
            </if>
            <if test="debianBootInfo != null">
                debian_boot_info,
            </if>
            <if test="cbsInfo != null">
                cbs_info,
            </if>
            <if test="nodeId != null">
                node_id,
            </if>
            <if test="position != null">
                position,
            </if>
            <if test="netStorageResFlag != null">
                net_storage_res_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dcId != null">
                #{dcId,jdbcType=BIGINT},
            </if>
            <if test="deviceLevel != null">
                #{deviceLevel,jdbcType=VARCHAR},
            </if>
            <if test="deviceCode != null">
                #{deviceCode,jdbcType=VARCHAR},
            </if>
            <if test="deviceOutCode != null">
                #{deviceOutCode,jdbcType=VARCHAR},
            </if>
            <if test="cloudVendorType != null">
                #{cloudVendorType,jdbcType=INTEGER},
            </if>
            <if test="deviceStatus != null">
                #{deviceStatus,jdbcType=BOOLEAN},
            </if>
            <if test="deviceIp != null">
                #{deviceIp,jdbcType=VARCHAR},
            </if>
            <if test="idc != null">
                #{idc,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="armServerCode != null">
                #{armServerCode,jdbcType=VARCHAR},
            </if>
            <if test="socModel != null">
                #{socModel,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=INTEGER},
            </if>
            <if test="padAllocationStatus != null">
                #{padAllocationStatus},
            </if>

            <if test="macAddress != null">
                #{macAddress},
            </if>
            <if test="initStatus != null">
                #{initStatus},
            </if>
            <if test="debianSysInfo != null">
                #{debianSysInfo},
            </if>
            <if test="debianBootInfo != null">
                #{debianBootInfo},
            </if>
            <if test="cbsInfo != null">
                #{cbsInfo},
            </if>
            <if test="nodeId != null">
                #{nodeId},
            </if>
            <if test="position != null">
                #{position},
            </if>
            <if test="netStorageResFlag != null">
                #{netStorageResFlag},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="net.armcloud.paascenter.common.model.entity.paas.Device">
        update device
        <set>
            <if test="dcId != null">
                dc_id = #{dcId,jdbcType=BIGINT},
            </if>
            <if test="deviceLevel != null">
                device_level = #{deviceLevel,jdbcType=VARCHAR},
            </if>
            <if test="deviceCode != null">
                device_code = #{deviceCode,jdbcType=VARCHAR},
            </if>
            <if test="deviceOutCode != null">
                device_out_code = #{deviceOutCode,jdbcType=VARCHAR},
            </if>
            <if test="cloudVendorType != null">
                cloud_vendor_type = #{cloudVendorType,jdbcType=INTEGER},
            </if>
            <if test="deviceStatus != null">
                device_status = #{deviceStatus,jdbcType=BOOLEAN},
            </if>
            <if test="deviceIp != null">
                device_ip = #{deviceIp,jdbcType=VARCHAR},
            </if>
            <if test="idc != null">
                idc = #{idc,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="armServerCode != null">
                arm_server_code = #{armServerCode,jdbcType=BIGINT},
            </if>
            <if test="socModel != null">
                soc_model = #{socModel,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="padAllocationStatus != null">
                pad_allocation_status = #{padAllocationStatus},
            </if>
            <if test="macAddress != null">
                mac_address = #{macAddress},
            </if>
            <if test="initStatus != null">
                init_status = #{initStatus},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="deleteDeviceByArmServerCode">
        update device
        set delete_flag = 1
        where arm_server_code =#{armServerCode}
    </update>

    <update id="updateDeviceLevelByDeviceCode">
        update device
        set device_level = #{deviceLevel}
        where device_code in
        <foreach item="item" collection="subList" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>


    <select id="listAllSystemInitializationDeviceVO" resultType="net.armcloud.paas.manage.model.vo.SystemInitializationDeviceVO">
        select d.device_ip          as deviceIp,
               ec.cluster_public_ip as clusterPublicIp
        from device d
                 join arm_server server on d.arm_server_code = server.arm_server_code
                 join edge_cluster ec on server.cluster_code = ec.cluster_code
        where d.delete_flag = false
          and server.delete_flag = false
          and ec.delete_flag = false
    </select>

    <select id="selectGatewayByCode" resultType="net.armcloud.paas.manage.model.vo.DeviceGatewayVO">
        select id, device_code, gateway,arm_server_code
        from device
        where device_code = #{deviceCode}
          and delete_flag = 0
    </select>

    <select id="listSystemInitializationDeviceVOByIp" resultType="net.armcloud.paas.manage.model.vo.SystemInitializationDeviceVO">
        select d.device_ip          as deviceIp,
               ec.cluster_public_ip as clusterPublicIp
        from device d
                 join arm_server server on d.arm_server_code = server.arm_server_code
                 join edge_cluster ec on server.cluster_code = ec.cluster_code
        where d.delete_flag = false
          and server.delete_flag = false
          and ec.delete_flag = false
          and d.device_ip in
        <foreach collection="deviceIps" item="ip" open="(" separator="," close=")">
            #{ip}
        </foreach>
    </select>

    <select id="getDeviceInfos" resultType="net.armcloud.paas.manage.model.vo.DeviceItemVO">
        select
        ase.arm_ip as armIp, de.device_out_code as deviceOutCode, de.node_id as nodeId, de.position as position, de.device_status as deviceStatus, de.device_code as deviceCode,
        de.device_ip as deviceIp, gde.netmask as netmask, gde.gateway as gateway, gde.dns as dns, de.mac_address as macAddress,de.debian_sys_info as debianSysInfo,
        de.debian_boot_info as debianBootInfo
        from device de
        left join arm_server ase on ase.arm_server_code = de.arm_server_code
        left join gateway_device gde on ase.gateway_device_id = gde.id
        where de.delete_flag = 0
        and de.arm_server_code = #{armServiceCode}
        <if test="nodeId != null ">
            and de.node_id = #{nodeId}
        </if>
        <if test="deviceStatus != null ">
            and de.device_status = #{deviceStatus}
        </if>
    </select>
    <select id="getDeviceLevel" resultType="net.armcloud.paas.manage.model.vo.DeviceVO">
        SELECT
        a.*,b.customer_id
        FROM
        device a
        left join arm_server arm on arm.arm_server_code = a.arm_server_code
        inner join edge_cluster ec on ec.cluster_code = arm.cluster_code
        LEFT JOIN customer_device b ON a.id = b.device_id and b.delete_flag = 0
        WHERE a.delete_flag = 0 and a.net_storage_res_flag = 1
        <if test="customerId != null ">
            and b.customer_id = #{customerId}
        </if>
        <if test="deviceLevel != null and deviceLevel != '' ">
            and a.device_level = #{deviceLevel}
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and arm.cluster_code = #{clusterCode}
        </if>
        <if test="dcCode != null and dcCode != '' ">
            and ec.dc_code = #{dcCode}
        </if>
    </select>
    <select id="getBmcInfo" resultType="net.armcloud.paas.manage.model.vo.DeviceItemVO"
            parameterType="net.armcloud.paas.manage.model.req.BmcListQueryReq">
        select
        ase.arm_ip as armIp, de.device_out_code as deviceOutCode, de.node_id as nodeId, de.position as position, de.device_status as deviceStatus, de.device_code as deviceCode,
        de.device_ip as deviceIp, gde.netmask as netmask, gde.gateway as gateway, gde.dns as dns, de.mac_address as macAddress,de.debian_sys_info as debianSysInfo,
        de.debian_boot_info as debianBootInfo
        from device de
        left join arm_server ase on ase.arm_server_code = de.arm_server_code
        left join gateway_device gde on ase.gateway_device_id = gde.id
        where de.delete_flag = 0
        <if test="query.debianBootInfo != null and query.debianBootInfo != ''">
            AND  de.debian_boot_info = #{query.debianBootInfo}
        </if>
        <if test="query.debianSysInfo != null and query.debianSysInfo != ''">
            AND  de.debian_sys_info = #{query.debianSysInfo}
        </if>
        <if test="query.deviceCode != null and query.deviceCode != ''">
            AND de.device_code LIKE CONCAT('%', #{query.deviceCode}, '%')
        </if>
        <if test="query.deviceIp != null and query.deviceIp != ''">
            AND de.device_ip LIKE CONCAT('%', #{query.deviceIp}, '%')
        </if>
        <if test="query.deviceStatus != null and query.deviceStatus != ''">
            AND  de.device_status = #{query.deviceStatus}
        </if>
        <if test="query.serverIp != null and query.serverIp != ''">
            AND ase.arm_ip LIKE CONCAT('%', #{query.serverIp}, '%')
        </if>

    </select>
    <select id="existDeviceCode" resultType="int">
        select count(1) from device
        where device_code = #{deviceCode}
    </select>

    <select id="selectDeviceByArmServerCodeAssigned" resultType="net.armcloud.paas.manage.model.vo.DeviceVO">
        select device_ip as deviceIp,device_status as status,pad_allocation_status as padAllocationStatus,device_code as deviceCode,
               device_out_code as deviceOutCode
        from device
        where arm_server_code = #{armServerCode} and init_status = 1
          and delete_flag = 0
    </select>
</mapper>
