<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.EdgeClusterMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="cluster_code" jdbcType="VARCHAR" property="clusterCode"/>
        <result column="dc_code" jdbcType="VARCHAR" property="dcCode"/>
        <result column="online_status" jdbcType="TINYINT" property="onlineStatus"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="cluster_public_ip" jdbcType="VARCHAR" property="clusterPublicIp"/>
        <result column="server_subnet_ip" jdbcType="VARCHAR" property="serverSubnetIp"/>
        <result column="device_net" jdbcType="VARCHAR" property="deviceNet"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag"/>
        <result column="server_num" jdbcType="INTEGER" property="serverNum"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="EdgeClusterVO" type="net.armcloud.paas.manage.model.vo.EdgeClusterVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="cluster_code" jdbcType="VARCHAR" property="clusterCode"/>
        <result column="dc_name" jdbcType="BIGINT" property="dcName"/>
        <result column="cluster_public_ip" jdbcType="VARCHAR" property="publicIp"/>
        <result column="server_subnet_ip" jdbcType="VARCHAR" property="subnetIp"/>
        <result column="device_net" jdbcType="VARCHAR" property="deviceNet"/>
        <result column="serverNum" jdbcType="INTEGER" property="serverNum"/>
        <result column="" jdbcType="INTEGER" property="padNum"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="online_status" jdbcType="VARCHAR" property="onlineStatus"/>
        <result column="cluster_type"  property="clusterType"/>
        <result column="storage_capacity"  property="storageCapacity"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , cluster_name, cluster_code, dc_code, online_status, status, cluster_public_ip,cluster_type,storage_capacity,
    server_subnet_ip, device_net ,remarks, delete_flag, server_num,create_by, create_time, update_by, update_time
    </sql>
    <sql id="EdgeClusterVO">
        ec
        .
        id
        , ec.cluster_name, ec.cluster_code, d.dc_name, ec.online_status, ec.status, ec.cluster_public_ip,
    ec.server_subnet_ip, ec.remarks,ec.device_net, dc.dict_label AS onlineStatusName,dc1.dict_label AS statusName,ec.online_status,ec.status,
    (select count(1) from arm_server where cluster_code = ec.cluster_code and delete_flag = 0) as serverNum,ec.cluster_type,ec.storage_capacity
    </sql>
    <select id="selectCountByClusterCode">
        select count(1)
        from arm_server
        where cluster_code = #{cluster_code}
    </select>
    <delete id="deleteEdgeCluster">
        update edge_cluster
        set delete_flag = 1
        where cluster_code = #{code}
    </delete>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from edge_cluster
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="listEdgeCluster" resultMap="EdgeClusterVO">
        select
        <include refid="EdgeClusterVO"/>
        from edge_cluster ec
        left join dc_info d on ec.dc_code = d.dc_code
        left JOIN dict dc on dc.dict_value = ec.online_status and dc.dict_type='operation_cluster_online_status'
        left JOIN dict dc1 on dc1.dict_value = ec.status and dc1.dict_type='operation_cluster_enable_status'
        <where>
            ec.delete_flag = 0
            <if test="clusterName != null and clusterName != ''">
                AND ec.cluster_name = #{clusterName}
            </if>
            <if test="clusterCode != null and clusterCode != ''">
                AND ec.cluster_code = #{clusterCode}
            </if>
            <if test="clusterPublicIp != null and clusterPublicIp != ''">
                AND ec.cluster_public_ip = #{clusterPublicIp}
            </if>
            <if test="onlineStatus != null">
                AND ec.online_status = #{onlineStatus}
            </if>
            <if test="status != null">
                AND ec.status = #{status}
            </if>
        </where>
    </select>
    <select id="detailEdgeCluster" resultMap="EdgeClusterVO">
        SELECT
            a.id,
            a.cluster_name,
            a.cluster_code,
            a.dc_code,
            a.online_status,
            a.status,
            a.cluster_public_ip,
            a.cluster_type,
            a.storage_capacity,
            a.server_subnet_ip,
            a.device_net,
            a.remarks,
            a.delete_flag,
            a.server_num,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            b.dc_name
        FROM edge_cluster a
        left join dc_info b on a.dc_code = b.dc_code
        where a.cluster_code = #{code}
        and a.delete_flag = 0 limit 1;
    </select>

    <select id="selectionListEdgeCluster" resultType="net.armcloud.paas.manage.model.vo.EdgeClusterVO">
        select
        ec.id, ec.cluster_name, ec.cluster_code as clusterCode,ec.cluster_public_ip as publicIp,ec.server_subnet_ip as
        subnetIp, ec.remarks,ec.cluster_type,ec.storage_capacity
        from edge_cluster ec
        <where>
            ec.online_status = 1 and ec.status = 1 and ec.delete_flag = 0
            <if test="clusterName != null">
                AND ec.cluster_name = #{clusterName}
            </if>
            <if test="clusterCode != null">
                AND ec.cluster_code = #{clusterCode}
            </if>
            <if test="serverSubnetIp != null">
                AND ec.server_subnet_ip = #{serverSubnetIp}
            </if>
            <if test="status != null">
                AND ec.status = #{status}
            </if>
            <if test="clusterType != null">
                AND ec.cluster_type = #{clusterType}
            </if>

        </where>
    </select>
    <select id="selectClusterByServerSubnet" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where server_subnet_ip = #{ipv4Cidr} and delete_flag = 0
    </select>
    <select id="selectClusterByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where cluster_name = #{clusterName}
        and delete_flag = 0
    </select>
    <select id="selectClusterByClusterCode" resultType="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where cluster_code = #{clusterCode} and delete_flag = 0
    </select>
    <select id="selectClusterByDeviceSubNet" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where device_subnet = #{deviceSubNet} and delete_flag = 0
    </select>
    <select id="selectListEdgeCluster" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where online_status = 1 and status = 1 and delete_flag = 0
    </select>
    <select id="getListEdgeCluster" resultType="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster">
        select
        <include refid="Base_Column_List"/>
        from edge_cluster
        where delete_flag = 0
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateEdgeClusterStatus">
        update edge_cluster
        set status = #{status}
        where cluster_code = #{code}
          and delete_flag = 0
    </update>

    <insert id="saveEdgeCluster" keyColumn="id" keyProperty="id"
            parameterType="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster" useGeneratedKeys="true">
        insert into edge_cluster
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clusterName != null">
                cluster_name,
            </if>
            <if test="clusterCode != null">
                cluster_code,
            </if>
            <if test="dcCode != null">
                dc_code,
            </if>
            <if test="onlineStatus != null">
                online_status,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="clusterPublicIp != null">
                cluster_public_ip,
            </if>
            <if test="serverSubnetIp != null">
                server_subnet_ip,
            </if>
            <if test="deviceNet != null">
                device_net,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="serverNum != null">
                server_num,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="clusterType != null">
                cluster_type,
            </if>
            <if test="storageCapacity != null">
                storage_capacity,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clusterName != null">
                #{clusterName,jdbcType=VARCHAR},
            </if>
            <if test="clusterCode != null">
                #{clusterCode,jdbcType=VARCHAR},
            </if>
            <if test="dcCode != null">
                #{dcCode,jdbcType=BIGINT},
            </if>
            <if test="onlineStatus != null">
                #{onlineStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="clusterPublicIp != null">
                #{clusterPublicIp,jdbcType=VARCHAR},
            </if>
            <if test="serverSubnetIp != null">
                #{serverSubnetIp,jdbcType=VARCHAR},
            </if>
            <if test="deviceNet != null">
                #{deviceNet,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="serverNum != null">
                #{serverNum,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clusterType != null">
                #{clusterType},
            </if>
            <if test="storageCapacity != null">
                #{storageCapacity},
            </if>
        </trim>
    </insert>
    <update id="updateEdgeCluster" parameterType="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster">
        update edge_cluster
        <set>
            <if test="clusterName != null">
                cluster_name = #{clusterName,jdbcType=VARCHAR},
            </if>
            <if test="clusterCode != null">
                cluster_code = #{clusterCode,jdbcType=VARCHAR},
            </if>
            <if test="dcCode != null">
                dc_code = #{dcCode,jdbcType=BIGINT},
            </if>
            <if test="onlineStatus != null">
                online_status = #{onlineStatus,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="clusterPublicIp != null">
                cluster_public_ip = #{clusterPublicIp,jdbcType=VARCHAR},
            </if>
            <if test="serverSubnetIp != null">
                server_subnet_ip = #{serverSubnetIp,jdbcType=VARCHAR},
            </if>
            <if test="deviceNet != null">
                device_net = #{deviceNet,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="serverNum != null">
                server_num = #{serverNum,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clusterType != null">
                cluster_type = #{clusterType},
            </if>
            <if test="storageCapacity != null">
                storage_capacity = #{storageCapacity},
            </if>

        </set>
        where cluster_code = #{clusterCode,jdbcType=VARCHAR}
    </update>
    <update id="updateEdgeClusterStatusByIp">
        update edge_cluster
        set status = {status}
        where ip = #{ip}
          and delete_flag = 1
    </update>
    <update id="updateEdgeClusterByClusterCode">
        update edge_cluster
        set server_num = #{serverCode}
        where cluster_code = #{clusterCode,jdbcType=VARCHAR}
    </update>

    <select id="selectEdgeClusterForUpdate" parameterType="String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
            FROM edge_cluster
        WHERE cluster_code = #{clusterCode, jdbcType=VARCHAR}
            FOR UPDATE
    </select>

    <select id="selectClusterByArmServerCodeAndStatusAndOnline" resultType="net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO">
        SELECT cluster_code,
               cluster_public_ip,
               dc_code from edge_cluster
        where cluster_code = #{clusterCode} and online_status = #{online} and status = #{status}
    </select>
    <select id="selectAllStorageCapacity" resultType="java.lang.Long">
        SELECT IFNULL(SUM(storage_capacity),0) FROM edge_cluster where delete_flag = 0
        <if test="clusterCode != null and clusterCode!=''">
           and  cluster_code = #{clusterCode}
        </if>
        <if test="dcCode != null and dcCode!= ''">
            and dc_code = #{dcCode}
        </if>
    </select>

    <select id="selectEdgeClusterCodeByDeviceIpSingle" resultType="java.lang.String">
        SELECT t4.cluster_code
        FROM   device t3
                   LEFT JOIN arm_server t4 on t4.arm_server_code = t3.arm_server_code and t4.delete_flag = 0
        where t3.device_ip = #{deviceIp}
          and t3.delete_flag = 0
            limit 1
    </select>

</mapper>