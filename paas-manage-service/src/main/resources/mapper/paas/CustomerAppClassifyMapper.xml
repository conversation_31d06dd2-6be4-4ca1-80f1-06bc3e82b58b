<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paas.manage.mapper.paas.CustomerAppClassifyMapper">

    <insert id="cusInsert" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customer_app_classify (customer_id,classify_name,classify_type, remark,create_time, create_by,update_time, update_by,delete_flag)
        VALUES (#{customerId},#{classifyName},#{classifyType},#{remark},#{createTime}, #{createBy}, #{updateTime}, #{updateBy},#{deleteFlag})
    </insert>

    <update id="updatePadNum">
        UPDATE customer_app_classify SET pad_num = pad_num + #{padNum} WHERE id = #{id}
    </update>
</mapper>