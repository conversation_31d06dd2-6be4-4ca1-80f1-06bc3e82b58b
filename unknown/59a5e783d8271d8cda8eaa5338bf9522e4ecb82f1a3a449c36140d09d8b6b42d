package net.armcloud.paas.manage.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import io.jsonwebtoken.lang.Collections;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.authorization.annotation.RequireAuthorization;
import net.armcloud.paas.manage.authorization.enums.OperationModuleEnum;
import net.armcloud.paas.manage.client.internal.dto.ExecuteADBFacadeDTO;
import net.armcloud.paas.manage.client.internal.dto.PowerResetDTO;
import net.armcloud.paas.manage.client.internal.dto.UpgradeImageDTO;
import net.armcloud.paas.manage.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paas.manage.constant.Constants;
import net.armcloud.paas.manage.constant.TaskStatusConstants;
import net.armcloud.paas.manage.constant.TokenConstants;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.R;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.exception.BasicException;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.dto.rtc.OpenOnlineAdbDTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.redis.contstant.RedisKeyPrefix;
import net.armcloud.paas.manage.service.ITaskService;
import net.armcloud.paas.manage.utils.ExportUtils;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenConsoleDTO;
import net.armcloud.paascenter.common.model.entity.manage.PadOnlineAndOffline;
import net.armcloud.paascenter.common.model.vo.api.AsyncCmdVO;
import net.armcloud.paascenter.common.model.vo.api.PadAdbVO;
import net.armcloud.paascenter.common.model.vo.api.StsTokenVO;
import net.armcloud.paascenter.common.model.vo.api.SyncCmdVO;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import net.armcloud.paascenter.common.model.dto.api.LimitBandwidthDTO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import net.armcloud.paas.manage.service.IDeviceService;
import net.armcloud.paas.manage.service.IPadService;
import net.armcloud.paas.manage.service.IpadGroupService;
import net.armcloud.paas.manage.service.impl.CustomerDeviceRecallServiceImpl;
import net.armcloud.paas.manage.service.IDevicePadService;
import net.armcloud.paas.manage.client.internal.stub.PadInternalFeignStub;
import net.armcloud.paas.manage.client.internal.stub.DeviceInternalFeignStub;
import net.armcloud.paas.manage.client.internal.facade.PadInternalFacade;
import net.armcloud.paas.manage.redis.service.RedisService;
import net.armcloud.paas.manage.client.internal.stub.RoomManageInternalStub;

import net.armcloud.paascenter.common.model.dto.api.SshAdbConnectDTO;
import net.armcloud.paascenter.common.model.dto.api.NewPadsDTO;
import net.armcloud.paascenter.common.model.dto.api.PadModelDTO;
import net.armcloud.paascenter.common.model.dto.api.ReplacePadTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.PadAdbDTO;
import net.armcloud.paascenter.common.model.dto.api.PadBackupDTO;
import net.armcloud.paascenter.common.model.dto.api.PadRestoreDTO;




import static cn.hutool.core.util.ObjectUtil.isEmpty;
import static net.armcloud.paas.manage.constant.StatusConstant.MANAGE;
import static net.armcloud.paas.manage.constant.StatusConstant.MANAGE_UUID;
import static net.armcloud.paas.manage.exception.code.ManageExceptionCode.PAD_CONNECT_EXCEPTION;
import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;
import static net.armcloud.paas.manage.constant.NumberConsts.TWO;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "资源管理")
public class ResourceManageController {

    @Resource
    private IDeviceService deviceService;
    @Resource
    private IPadService padService;
    @Resource
    private IpadGroupService padGroupService;
    @Resource
    private PadInternalFeignStub padInternalFeignStub;
    @Resource
    private CustomerDeviceRecallServiceImpl customerDeviceRecallService;
    @Resource
    private DeviceInternalFeignStub deviceInternalFeignStub;
    @Resource
    private PadInternalFacade padInternalFacade;
    @Resource
    private RedisService redisService;
    @Resource
    private RoomManageInternalStub roomManageInternalStub;
    @Resource
    private IDevicePadService devicePadService;
    @Resource
    private ITaskService taskService;

    @Deprecated
    @RequestMapping(value = "/manage/open/resource/padAllocationDeviceList", method = RequestMethod.POST)
    @ApiOperation(value = "实例已分配物理机列表", httpMethod = "POST", notes = "实例已分配物理机列表")
    public Result<List<String>> padAllocationDeviceList(@RequestBody PadAllocationDeviceDTO param) {
        List<String> list = deviceService.padAllocationDeviceList(param);
        return Result.ok(list);
    }

    @RequestMapping(value = "/manage/open/resource/deviceStatus", method = RequestMethod.POST)
    @ApiOperation(value = "查询板卡状态", httpMethod = "POST", notes = "查询板卡状态")
    public Result<List<Map<String, String>>> deviceStatus(@RequestBody List<String> deviceCodes) {
        List<Map<String, String>> list = deviceService.queryDeviceStatus(deviceCodes);
        return Result.ok(list);
    }

    @RequestMapping(value = "/manage/open/resource/queryDeviceList", method = RequestMethod.POST)
    @ApiOperation(value = "物理机列表", httpMethod = "POST", notes = "物理机列表")
    public Result<Page<DeviceVO>> queryDeviceList(@RequestBody DeviceDTO param) {
        Page<DeviceVO> deviceVOS = deviceService.queryDeviceList(param);
        return Result.ok(deviceVOS);
    }

    @RequestMapping(value = "/manage/open/resource/queryDeviceListOnlineAndOffline", method = RequestMethod.POST)
    @ApiOperation(value = "物理机在线离线数量", httpMethod = "POST", notes = "物理机在线离线数量")
    public Result<List<PadOnlineAndOffline>> queryDeviceListOnlineAndOffline() {
        return Result.ok(deviceService.queryDeviceListOnlineAndOffline());
    }

    @RequestMapping(value = "/manage/open/resource/queryPadList", method = RequestMethod.POST)
    @ApiOperation(value = "实例列表", httpMethod = "POST", notes = "实例列表")
    public Result<Page<PadVO>> queryPadList(@RequestBody PadDTO param) {
        String padCodes = param.getPadCodes();
        if (StrUtil.isNotBlank(padCodes)) {
            List<String> padCodeList = CollUtil.toList(padCodes.split(","));
            param.setPadCodeList(padCodeList);
        }
        param.paramConvert();
        Page<PadVO> deviceVOS = padService.listPads(param);
        return Result.ok(deviceVOS);
    }


    @RequestMapping(value = "/manage/open/resource/queryPadList/export", method = RequestMethod.POST)
    @ApiOperation(value = "实例列表", httpMethod = "POST", notes = "实例列表导出")
    public void queryPadListExport(@RequestBody PadDTO param, HttpServletResponse response) {
        String padCodes = param.getPadCodes();
        if (StrUtil.isNotBlank(padCodes)) {
            List<String> padCodeList = CollUtil.toList(padCodes.split(","));
            param.setPadCodeList(padCodeList);
        }
        List<PadVO> padVOS = padService.exportListPads(param);
        log.info("export_padVos:{}",padVOS.size());
        ExportUtils.HttpExport(response,PadVO.class,padVOS,"实例详细信息","实例详细信息");

    }


    @ApiOperation(value = "资源分配", httpMethod = "POST", notes = "资源分配")
    @RequestMapping(value = "/manage/open/resource/allocateResource", method = RequestMethod.POST)
    public Result<?> allocateResource(@RequestBody SourceDTO param) {
        return deviceService.allocateResource(param);
    }

    @ApiOperation(value = "资源分配记录", httpMethod = "POST", notes = "资源分配记录")
    @RequestMapping(value = "/manage/open/resource/allocateResourceRecord", method = RequestMethod.POST)
    public Result<Page<DeviceVO>> allocateResourceRecord(@RequestBody DeviceDTO param) {
        Page<DeviceVO> deviceVOPage = deviceService.allocateResourceRecord(param);
        return Result.ok(deviceVOPage);
    }

    @ApiOperation(value = "云机回收记录", httpMethod = "POST", notes = "云机回收记录")
    @RequestMapping(value = "/manage/open/resource/deviceRecall", method = RequestMethod.POST)
    public Result<Page<DeviceVO>> deviceRecall(@RequestBody DeviceDTO param) {
        Page<DeviceVO> deviceVOPage = customerDeviceRecallService.queryDeviceList(param);
        return Result.ok(deviceVOPage);
    }

    @ApiOperation(value = "查询分组", httpMethod = "GET", notes = "查询分组")
    @ApiImplicitParams({@ApiImplicitParam(name = "customerId", value = "customerId", paramType = "query")})
    @RequestMapping(value = "/manage/open/resource/queryGroup", method = RequestMethod.GET)
    public Result<List<PadGroupVO>> queryGroup(Long customerId) {
        List<PadGroupVO> padGroups = padGroupService.queryPadGroup(customerId);
        return Result.ok(padGroups);
    }

    @ApiOperation(value = "查询客户列表", httpMethod = "GET", notes = "查询客户列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "query", value = "查询条件", paramType = "query")})
    @RequestMapping(value = "/manage/open/resource/queryCustomerList", method = RequestMethod.GET)
    public Result<List<CustomerVO>> queryCustomerList(String query) {
        List<CustomerVO> customerVOS = deviceService.queryCustomerList(query);
        return Result.ok(customerVOS);
    }

    @ApiOperation(value = "回收资源", httpMethod = "POST", notes = "回收资源")
    @ApiImplicitParams({@ApiImplicitParam(name = "deviceId", value = "物理机id", paramType = "body", dataType = "String", allowMultiple = true)})
    @RequestMapping(value = "/manage/open/resource/recoveryResource", method = RequestMethod.POST)
    public Result<?> recoveryResource(@RequestBody List<String> deviceId) {
        deviceService.recoveryResource(deviceId);
        return Result.ok();
    }

    @ApiOperation(value = "定时回收资源", httpMethod = "POST", notes = "定时回收资源")
    @ApiImplicitParams({@ApiImplicitParam(name = "deviceIds", value = "物理机id", paramType = "body", dataType = "String", allowMultiple = true)})
    @RequestMapping(value = "/manage/open/resource/timeRecoveryResource", method = RequestMethod.POST)
    public Result<?> timeRecoveryResource(@RequestBody List<String> deviceIds, @RequestParam("recoveryTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm") Date recoveryTime) {
        deviceService.timeRecoveryResource(deviceIds, recoveryTime);
        return Result.ok();
    }

    @ApiOperation(value = "实例重启", httpMethod = "POST", notes = "实例重启")
    @ApiImplicitParams({@ApiImplicitParam(name = "padCodes", value = "padCodes", dataType = "String", paramType = "body", allowMultiple = true)})
    @RequestMapping(value = "/manage/open/resource/restartPad", method = RequestMethod.POST)
    public Result<?> restartPad(@RequestBody List<String> padCodes) {
        padService.restart(padCodes);
        return Result.ok();
    }

    @ApiOperation(value = "实例重置", httpMethod = "POST", notes = "实例重置")
    @ApiImplicitParams({@ApiImplicitParam(name = "padCodes", value = "padCodes", paramType = "body", dataType = "String", allowMultiple = true)})
    @RequestMapping(value = "/manage/open/resource/resetPad", method = RequestMethod.POST)
    public Result<?> resetPad(@RequestBody List<String> padCodes) {
        padService.reset(padCodes);
        return Result.ok();
    }

    @ApiOperation(value = "物理机重启", httpMethod = "POST", notes = "物理机重启")
    @RequestMapping(value = "/manage/open/resource/restartDevice", method = RequestMethod.POST)
    @RequireAuthorization(module = OperationModuleEnum.DEVICE_RESTART, resourceCode = "#deviceIds")
    public Result<?> restartDevice(@RequestBody List<String> deviceIds) {
        List<DeviceVO> deviceList = deviceService.getDeviceInfo(deviceIds);
        if (Collections.isEmpty(deviceList)) {
            return Result.fail("无有效云机！");
        }
        //检测是否有已存在的任务
        List<Integer> status = Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus());
        List<String> deviceCodes = deviceList.stream().map(a -> a.getDeviceCode()).collect(Collectors.toList());
        List<String> pads = devicePadService.selectPadByDeviceCode(deviceCodes);
        if (CollUtil.isNotEmpty(pads)) {
            int count = taskService.selectTaskByTaskTypeAndTaskStatus(pads, status);
            if (count > 0) {
                return Result.fail("板卡上的实例存在进行的任务！");
            }
        }

        PowerResetDTO powerResetDTO = new PowerResetDTO();
        powerResetDTO.setDeviceIps(deviceIds);
        powerResetDTO.setCustomerId(SecurityUtils.getUserId());
        powerResetDTO.setType(ZERO);
        powerResetDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        powerResetDTO.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        return deviceInternalFeignStub.powerReset(powerResetDTO);
    }

    @ApiOperation(value = "物理机断电重启", httpMethod = "POST", notes = "物理机断电重启")
    @RequestMapping(value = "/manage/open/resource/powerReset", method = RequestMethod.POST)
    @RequireAuthorization(module = OperationModuleEnum.POWER_RESET, resourceCode = "#deviceIds")
    public Result<?> powerReset(@RequestBody List<String> deviceIds) {
        List<DeviceVO> deviceList = deviceService.getDeviceInfo(deviceIds);
        if (Collections.isEmpty(deviceList)) {
            return Result.fail("无有效云机！");
        }
        //检测是否有已存在的任务
        List<Integer> status = Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus());
        List<String> deviceCodes = deviceList.stream().map(a -> a.getDeviceCode()).collect(Collectors.toList());
        List<String> pads = devicePadService.selectPadByDeviceCode(deviceCodes);
        if (CollUtil.isNotEmpty(pads)) {
            int count = taskService.selectTaskByTaskTypeAndTaskStatus(pads, status);
            if (count > 0) {
                return Result.fail("板卡上的实例存在进行的任务！");
            }
        }

        PowerResetDTO powerResetDTO = new PowerResetDTO();
        powerResetDTO.setDeviceIps(deviceIds);
        powerResetDTO.setCustomerId(SecurityUtils.getUserId());
        powerResetDTO.setType(TWO);
        powerResetDTO.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        powerResetDTO.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        return deviceInternalFeignStub.powerReset(powerResetDTO);
    }

    @PostMapping(value = "/manage/open/resource/enableADB")
    @ApiOperation(value = "开启ADB", httpMethod = "POST", notes = "开启ADB")
    public Result<?> enableADB(@RequestBody @Valid SshAdbConnectDTO param) {
        param.setUser(SecurityUtils.getUsername());
        return padInternalFeignStub.sshOrAdbConnect(param);
    }

    @PostMapping(value = "/manage/open/resource/upgradeImage")
    @ApiOperation(value = "批量升级镜像")
    public Result<?> upgradeImage(@RequestBody @Valid UpgradeImageDTO param) {
        log.info("升级镜像入参：{}", JSON.toJSONString(param));
        return Result.ok(padService.upgradeImage(param));
    }

    @PostMapping(value = "/manage/open/resource/deleteByDeviceCodeAndPadCode")
    @ApiOperation(value = "根据云机或实例删除数据（启朔）")
    public Result<?> deleteByDeviceCodeAndPadCode(@RequestBody DeleteByDeviceCodeAndPadCodeDTO param) {
        //根据传入的padCode或者deviceCode查询出对应的设备信息
        deviceService.deleteByDeviceCodeAndPadCode(param);
        return Result.ok();
    }

    @PostMapping(value = "/manage/open/resource/asyncCmd")
    @ApiOperation(value = "异步执行ADB命令", httpMethod = "POST", notes = "异步执行ADB命令")
    public Result<List<AsyncCmdVO>> asyncCmd(@RequestBody ExecuteADBFacadeDTO param) {
        padService.asyncCmd(param);
        return Result.ok();
    }

    @GetMapping(value = "/manage/open/resource/getStsToken")
    @ApiOperation(httpMethod = "GET", value = "获取SDK授权Token", notes = "获取SDK授权Token")
    public Result<StsTokenVO> stsToken(@RequestParam(value = "padCode", required = false) String padCode) {
        PadInfoVO padInfo = padService.getPadInfo(padCode);
        Long userId = isEmpty(padInfo) || isEmpty(padInfo.getCustomerId()) ? SecurityUtils.getUserId() : padInfo.getCustomerId();

        StsTokenVO stsTokenVO = new StsTokenVO();
        String token = UUID.randomUUID().toString();
        String value = userId + Constants.UNDERLINE + token;
        redisService.setCacheObject(RedisKeyPrefix.SDK_TOKEN + token, value, 24L, TimeUnit.HOURS);
        stsTokenVO.setToken(token);
        return Result.ok(stsTokenVO);
    }

    @PostMapping(value = "/manage/open/resource/validAuthorization")
    @ApiOperation(httpMethod = "POST", value = "校验授权", notes = "校验授权")
    @RequireAuthorization(module = OperationModuleEnum.CONNECT_CLOUD_MACHINE, resourceCode = "#customerIds")
    public Result<StsTokenVO> validAuthorization(@RequestBody List<String> customerIds ) {
        return Result.ok();
    }

    /**
     * MoreLogin需要使用padCode生成token,该token只能用于当前padCode下用户生成rtc信息
     * @param padCode
     * @return
     */
    @GetMapping(value = "/manage/open/resource/getStsTokenByPadCode")
    @ApiOperation(httpMethod = "GET", value = "根据padCode获取SDK授权Token", notes = "根据padCode获取SDK授权Token")
    public Result<StsTokenVO> stsTokenByPadCode(@RequestParam(value = "padCode") String padCode, @RequestParam(value = "customerId") String customerId ) {
        PadInfoVO padInfo = padService.getPadInfo(padCode);
        Long userId = isEmpty(padInfo) || isEmpty(padInfo.getCustomerId()) ? SecurityUtils.getUserId() : padInfo.getCustomerId();

        StsTokenVO stsTokenVO = new StsTokenVO();
        //为了向前兼容清除,在token后缀拼接指定字符串,并在value中拼接padCode信息.后续验证该token是否可以生成对应pad_code下的rtc信息,通过切割token跟value去判断
        String token = UUID.randomUUID()+ Constants.USER_MORELOGIN;
        String value = userId +Constants.UNDERLINE + token+Constants.UNDERLINE+padCode;
        redisService.setCacheObject(RedisKeyPrefix.SDK_TOKEN + token, value, 24L, TimeUnit.HOURS);
        stsTokenVO.setToken(token);
        return Result.ok(stsTokenVO);
    }

    @ApiOperation(value = "连接实例", httpMethod = "POST", notes = "连接实例")
    @RequestMapping(value = "/manage/open/resource/connectToken", method = RequestMethod.POST)
    public Result<?> connectToken(HttpServletRequest httpServletRequest, @Valid @RequestBody ConnectTokenDTO param) {
        String token = httpServletRequest.getHeader(TokenConstants.AUTHENTICATION);

        if (StrUtil.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StrUtil.EMPTY);
        }
        String userIdStr = "0";
        RoomTokenConsoleDTO dto = new RoomTokenConsoleDTO();
        dto.setUuid(MANAGE_UUID + userIdStr);
        dto.setValidaToken(token);
        dto.setUserId(MANAGE + userIdStr);
        dto.setCustomerId(SecurityUtils.getUserId());
        dto.setPadCode(param.getPadCode());
        dto.setExpire(param.getExpire());
        log.info("connectToken:{}", JSON.toJSONString(dto));
        Result<RoomTokenVO> result = roomManageInternalStub.getRoomToken(dto);
        ConnectTokenVO connectTokenVO = new ConnectTokenVO();
        if (result.getCode() == 200) {
            connectTokenVO.setRoomToken(result.getData().getRoomToken());
            connectTokenVO.setRoomCode(result.getData().getRoomCode());
            connectTokenVO.setAppId(result.getData().getAppId());
            connectTokenVO.setUserId(MANAGE + userIdStr);
            return Result.ok(connectTokenVO);
        } else {
            return result;
        }
    }

    @ApiOperation(value = "实例信息", httpMethod = "GET", notes = "实例信息")
    @GetMapping(value = "/manage/open/resource/padInfo")
    public Result<PadInfoVO> padInfo(@RequestParam String padCode) {
        return Result.ok(padService.getPadInfo(padCode));
    }


    @ApiOperation(value = "实例信息", httpMethod = "GET", notes = "实例信息")
    @PostMapping(value = "/manage/open/resource/padListStatus")
    public Result<List<PadStatusVO>> padListStatus(@RequestBody List<String> padCodes) {
        return Result.ok(padService.padListStatus(padCodes));
    }

    @PostMapping(value = "/manage/open/resource/newPads")
    @ApiOperation(value = "批量一键新机参数")
    public Result<List<GeneratePadTaskVO>> newPads(@RequestBody @Valid BatchNewPadsDTO dto) {
        NewPadsDTO param = new NewPadsDTO();
        List<PadModelDTO> padModels = new ArrayList<>();
        for (String padCode : dto.getPadCodes()) {
            PadModelDTO padModelDTO = new PadModelDTO();
            padModelDTO.setPadCode(padCode);
            padModels.add(padModelDTO);
        }
        param.setPadModels(padModels);
        param.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM);
        param.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        param.setCustomerId(SecurityUtils.getUserId());
        log.info("Batch New Pads----------------> param:{}", param);
        return padInternalFeignStub.newPads(param);
    }

    @PostMapping(value = "/manage/open/resource/replacePad")
    @ApiOperation(value = "一键新机")
    public Result replacePad(@RequestBody @Valid BatchNewPadsDTO dto) {
        Long userId = SecurityUtils.getUserId();
        ReplacePadTaskDTO padModelDTO = new ReplacePadTaskDTO();
        padModelDTO.setPadCodes(dto.getPadCodes());
        padModelDTO.setCustomerId(userId);
        padModelDTO.setReplacementRealAdiFlag(dto.getReplacementRealAdiFlag());
        padModelDTO.setCountryCode(dto.getCountryCode());
        padModelDTO.setRealPhoneTemplateId(dto.getRealPhoneTemplateId());
        if(StringUtils.isNotBlank(dto.getCertificate())){
            padModelDTO.setCertificate(dto.getCertificate());
        }
        try {
            padModelDTO.setAndroidProp(com.alibaba.fastjson.JSON.parseObject(dto.getAndroidProp()));
        } catch (Exception e) {
            return Result.fail("安卓改机属性格式错误");
        }
        Result<List<GeneratePadTaskVO>> result = padInternalFeignStub.replacePad(padModelDTO);
        if (R.SUCCESS != result.getCode()) {
            return Result.fail(result.getMsg());
        }
        return Result.ok();
    }

    @PostMapping(value = "/manage/open/resource/reBootStream")
    @ApiOperation(value = "重启推流服务", httpMethod = "POST", notes = "重启推流服务")
    public Result<List<AsyncCmdVO>> reBootStream(@RequestBody ExecuteADBFacadeDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        param.setCommand("kill -9 $(pidof com.cloud.rtcgesture)");
        return padInternalFacade.asyncCmd(param);
    }


    @PostMapping(value = "/manage/open/resource/openOnlineAdb")
    @ApiOperation(value = "开启关闭ADB(0关闭 1打开)", httpMethod = "POST", notes = "开启关闭ADB(0关闭 1打开)")
    public Result<List<SyncCmdVO>> openOnlineAdb(@RequestBody OpenOnlineAdbDTO param) {
        ExecuteADBFacadeDTO dto = new ExecuteADBFacadeDTO();
        dto.setPadCodes(param.getPadCodes());
        dto.setCommand("setprop persist.sys.cloud.madb_enable "+param.getStatus());
        dto.setCustomerId(SecurityUtils.getUserId());
        Result<List<SyncCmdVO>> result = padInternalFacade.syncCmd(dto);
        if(Objects.equals( result.getCode(), R.SUCCESS)){
            padService.updatePadAdbOpenStatus(param.getPadCodes(), param.getStatus());
        }
        return result;
    }

    @PostMapping("/manage/open/resource/setSpeed")
    @ApiOperation(value = "实例限速")
    public Result<?> limitBandwidth(@Valid @RequestBody LimitBandwidthDTO param) {
        padService.limitBandwidth(param);
        return Result.ok();
    }

    /**
     * 开启 / 关闭 adb
     * @param param
     * @return
     */
    @RequestMapping(value = "/manage/open/resource/adb", method = RequestMethod.POST)
    @ApiOperation(value = "实例开启/关闭adb")
    public Result<PadAdbVO> padAdb(@Valid @RequestBody PadAdbDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        try {
            return padInternalFeignStub.padAdbConnect(param);
        } catch (Exception e) {
            log.error("padAdbConnect error>>>> param:{}",JSON.toJSONString(param), e);
            throw new BasicException(PAD_CONNECT_EXCEPTION);
        }
    }

    @PostMapping("/manage/open/resource/backup")
    @ApiOperation(value = "数据备份")
    public Result<?> backup(@Valid @RequestBody PadBackupDTO param) {
        param.setCustomerId(SecurityUtils.getUserId());
        param.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM);
        return padInternalFeignStub.backup(param);
    }

    @PostMapping("/manage/open/resource/restore")
    @ApiOperation(value = "数据还原")
    public Result<?> restore(@Valid @RequestBody PadRestoreDTO param) {
        param.setTaskSource(SourceTargetEnum.ADMIN_SYSTEM);
        param.setCustomerId(SecurityUtils.getUserId());
        return padInternalFeignStub.restore(param);
    }
}
