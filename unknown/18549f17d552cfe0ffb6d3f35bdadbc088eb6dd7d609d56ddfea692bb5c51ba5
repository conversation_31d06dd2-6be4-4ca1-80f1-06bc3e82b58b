package net.armcloud.paas.manage.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Component
public class ThreadPoolMonitor {

    private final ThreadPoolExecutor threadPool;

    public ThreadPoolMonitor(ThreadPoolExecutor threadPool) {
        this.threadPool = threadPool;
    }

    @Scheduled(fixedDelay = 5000) // 每5秒监控一次
    public void monitor() {
        int activeCount = threadPool.getActiveCount();
        long completedTaskCount = threadPool.getCompletedTaskCount();
        int queueSize = threadPool.getQueue().size();
        long taskCount = threadPool.getTaskCount();

        log.info("WebSocket ThreadPool Status: Active Threads: {}, Completed Tasks: {}, Pending Tasks: {}, Total Tasks: {}",
                activeCount, completedTaskCount, queueSize, taskCount);
    }
}