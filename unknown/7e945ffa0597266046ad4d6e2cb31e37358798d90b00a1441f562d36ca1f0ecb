package net.armcloud.paas.manage.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PadVO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @ExcelIgnore
    private Long id;

    /**
     * 申请网存的数量
     */
    @ApiModelProperty(value = "netStorageResSize")
    @ExcelIgnore
    private Long netStorageResSize;


    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    @ExcelProperty(value = "实例编号", index = 0)

    private String padCode;

    /**
     * 实例信息-实例规格
     */
    @ApiModelProperty(value = "实例信息-实例规格")
    @ExcelProperty(value = "实例规格", index = 1)
    private String instanceType;

    /**
     * 实例信息-长连接状态
     */
    @ApiModelProperty(value = "实例信息-长连接状态")
    @ExcelIgnore
    private String longConnectionStatus;

    /**
     * 实例信息-长连接状态名称
     */
    @ApiModelProperty(value = "实例信息-长连接状态名称")
    @ExcelIgnore
    private String longConnectionStatusName;

    /**
     * 实例信息-连接地址
     */
    @ApiModelProperty(value = "实例信息-连接地址")
    @ExcelIgnore
    private String connectionAddress;

    /**
     * 实例存储总容量
     */
    @ExcelIgnore
    private Long dataSize;

    /**
     * 实例存储总容量
     */
    @ExcelProperty(value = "总容量", index = 5)

    private String exportDataSize;
    /**
     * 实例存储已使用容量
     */
    @ExcelIgnore
    private Long dataSizeUsed;

    @ExcelProperty(value = "已使用容量", index = 6)
    private String exportDataSizeUsed;
    /**
     * 实例信息-存储数据
     */
    @ApiModelProperty(value = "实例信息-存储数据")
    @ExcelIgnore
    private String dataSizeInfo;

    /**
     * 实例信息-存储数据
     */
    @ApiModelProperty(value = "网存实例-存储数据(使用大小/申请大小)")
    @ExcelIgnore
    private String netStorageDataSizeInfo;
    /**
     * 实例信息-应用数量
     */
    @ApiModelProperty(value = "实例信息-应用数量")
    @ExcelIgnore
    private Integer appCount;

    @ApiModelProperty(value = "实例信息-屏幕布局")
    @ExcelIgnore
    private String screenLayout;

    @ApiModelProperty(value = "实例信息-屏幕布局编码")
    @ExcelProperty(value = "屏幕布局", index = 4)
    private String screenLayoutInfo;

    @ApiModelProperty(value = "镜像信息-镜像id")
    @ExcelIgnore
    private String imageId;

    @ApiModelProperty(value = "镜像信息-镜像版本")
    @ExcelIgnore
    private String imageName;

    @ApiModelProperty(value = "镜像信息-镜像版本")
    @ExcelIgnore
    private String imageVersion;

    @ApiModelProperty(value = "镜像信息-镜像版本")
    @ExcelProperty(value = "镜像", index = 2)
    private String imageDetail;

    /**
     * 实例信息--上行限速
     */
    @ApiModelProperty(value = "网络信息--上行限速")
    @ExcelIgnore
    private String uploadSpeed;

    @ApiModelProperty(value = "网络信息--上行限速")
    @ExcelProperty(value = "上行限速", index = 11)
    private String exportUploadSpeed;

    /**
     * 实例信息--下行限速
     */
    @ApiModelProperty(value = "网络信息-下行限速")
    @ExcelIgnore
    private String downloadSpeed;

    /**
     * 实例信息--下行限速
     */
    @ApiModelProperty(value = "网络信息-下行限速")
    @ExcelProperty(value = "下行限速", index = 12)
    private String exportDownloadSpeed;


    @ApiModelProperty(value = "网络信息-ADB状态")
    @ExcelIgnore
    private String adbStatus;
    @ExcelIgnore
    @ApiModelProperty(value = "网络信息-ADB状态")
    private String adbStatusName;
    @ExcelIgnore
    @ApiModelProperty(value = "网络信息-ADB地址")
    private String adbAddress;
    @ExcelIgnore
    @ApiModelProperty(value = "网络信息-ADB密钥")
    private String adbKey;
    @ExcelIgnore
    @ApiModelProperty(value = "网络信息-ADB失效时间")
    private String adbExpireTime;
    @ExcelIgnore
    @ApiModelProperty(value = "实例信息-推流类型")
    private String pushType;

    /**
     * 外部信息-供应商
     */
    @ApiModelProperty(value = "外部信息-供应商")
    @ExcelIgnore
    private String cloudVendorType;

    /**
     * 外部信息-供应商名称
     */
    @ApiModelProperty(value = "外部信息-供应商名称")
    @ExcelIgnore
    private String cloudVendorTypeName;

    /**
     * 外部信息-所在机房
     */
    @ApiModelProperty(value = "外部信息-所在机房")
    @ExcelIgnore
    private String daName;

    /**
     * 外部信息-云机id
     */
    @ApiModelProperty(value = "外部信息-云机id")
    @ExcelIgnore
    private String deviceOutCode;

    /**
     * 外部信息-实例id
     */
    @ApiModelProperty(value = "外部信息-实例id")
    @ExcelIgnore
    private String instanceId;

    /**
     * 外部信息-实例SN号
     */
    @ApiModelProperty(value = "外部信息-实例SN号")
    @ExcelIgnore
    private String instanceSn;

    /**
     * 外部信息-实例ip
     */
    @ApiModelProperty(value = "外部信息-云机ip")
    @ExcelProperty(value = "板卡IP", index = 9)
    private String deviceIp;

    @ApiModelProperty(value = "外部信息-实例ip")
    @ExcelProperty(value = "实例IP", index = 3)
    private String padIp;

    @ApiModelProperty(value = "规格信息-集群code")
    @ExcelIgnore
    private String clusterCode;

    @ApiModelProperty(value = "规格信息-集群id")
    @ExcelIgnore
    private Long clusterId;

    /**
     * 归属客户-客户账户
     */
    @ApiModelProperty(value = "归属客户-客户账户")
    @ExcelProperty(value = "客户账号", index = 13)

    private String customerAccount;

    /**
     * 归属客户-客户id
     */
    @ApiModelProperty(value = "归属客户-客户id")
    @ExcelIgnore
    private Long customerId;

    @ApiModelProperty(value = "客户编号")
    @ExcelIgnore
    private String customerCode;

    /**
     * 归属客户-分配时间
     */
    @ApiModelProperty(value = "归属客户-分配时间")
    @ExcelIgnore
    private String assignTime;

    /**
     * 归属客户-到期时间
     */
    @ApiModelProperty(value = "归属客户-到期时间")
    @ExcelProperty(value = "到期时间", index = 15)

    private String expireTime;

    /**
     * 归属客户-分组名称
     */
    @ApiModelProperty(value = "归属客户-分组名称")
    @ExcelProperty(value = "分组名称", index = 14)
    private String groupName;

    /**
     * 状态-业务状态
     */
    @ApiModelProperty(value = "状态-业务状态")
    @ExcelIgnore
    private Integer status;

    /**
     * 状态-业务状态名称
     */
    @ApiModelProperty(value = "状态-业务状态名称")
    @ExcelIgnore
    private String statusName;

    /**
     * 状态-云机状态
     */
    @ApiModelProperty(value = "状态-云机状态")
    @ExcelIgnore
    private Integer cloudStatus;

    /**
     * 状态-云机状态名称
     */
    @ApiModelProperty(value = "状态-云机状态名称")
    @ExcelIgnore
    private String cloudStatusName;

    /**
     * 状态-实例状态
     */
    @ApiModelProperty(value = "状态-实例状态")
    @ExcelIgnore
    private Integer instanceStatus;

    /**
     * 状态-实例状态名称
     */
    @ApiModelProperty(value = "状态-实例状态名称")
    @ExcelIgnore
    private String instanceStatusName;

    /**
     * 状态-推流状态
     */
    @ApiModelProperty(value = "状态-推流状态")
    @ExcelIgnore
    private Integer pushStreamStatus;

    /**
     * 状态-推流状态名称
     */
    @ApiModelProperty(value = "状态-推流状态名称")
    @ExcelIgnore
    private String pushStreamStatusName;

    @ApiModelProperty(value = "云机编号")
    @ExcelProperty(value = "板卡编号", index = 8)

    private String deviceCode;

    @ApiModelProperty(value = "服务器状态")
    @ExcelIgnore
    private String armServerOnline;

    @ApiModelProperty(value = "服务器状态名称")
    @ExcelIgnore
    private String armServerOnlineName;

    @ApiModelProperty(value = "服务器编号")

    @ExcelProperty(value = "服务器编号", index = 7)

    private String armServerCode;

    @ApiModelProperty(value = "推流版本")
    @ExcelProperty(value = "RTC版本", index = 10)
    private String rtcVersionName;

    @ApiModelProperty(value = "app数量")
    @ExcelProperty(value = "安装应用数量", index = 16)
    private Integer appNumber;

    @ApiModelProperty(value = "实例在线状态")
    @ExcelIgnore
    private Integer padStatus;

    @ApiModelProperty(value = "adb开启状态(0关闭 1开启)")
    @ExcelProperty(value = "adb开启状态(0关闭 1开启)", index = 18)
    private String adbOpenStatus;


    @ApiModelProperty(value = "安装app详情")
    @ExcelProperty(value = "安装应用详情", index = 17)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String appsJson;


    @ApiModelProperty(value = "实例类型（virtual：虚拟机；real：真机）")
    @ExcelProperty(value = "实例类型（virtual：虚拟机；real：真机）", index = 19)
    private String padType;

    @ApiModelProperty(value = "服务器ip")
    @ExcelIgnore
    private String armServerIp;

    @ApiModelProperty(value = "申请存储大小")
    @ExcelIgnore
    private String netStorageResApplySize;

    @ApiModelProperty(value = "实际使用大小")
    @ExcelIgnore
    private String netStorageResUseSize;

    @ApiModelProperty(value = "板卡用户ID")
    @ExcelIgnore
    private Long deviceCustomerId;

    @ApiModelProperty(value = "分配时间")
    @ExcelIgnore
    private String createTime;

    @ApiModelProperty(value = "实例dns")
    private String dns;

}
