package net.armcloud.paas.manage.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TaskBackupVO implements Serializable {

    /**
     * 备份时间
     */
    @ApiModelProperty(value = "备份时间")
    private String createTime;

    /**
     * 备份id
     */
    @ApiModelProperty(value = "备份id")
    private Long id;

    /**
     * 备份状态
     */
    @ApiModelProperty(value = "备份状态")
    private Integer status;

    /**
     * 备份状态
     */
    @ApiModelProperty(value = "备份状态")
    private String statusName;

    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    private String padCode;

    /**
     * 镜像ID
     */
    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    /**
     * 备份名称
     */
    @ApiModelProperty(value = "备份名称")
    private String backupName;

    /**
     * 备份名称
     */
    @ApiModelProperty(value = "备份大小")
    private Long backupSize;

    /**
     * 备份类型
     */
    @ApiModelProperty(value = "备份类型")
    private Integer backupType;

    /**
     * 备份类型
     */
    @ApiModelProperty(value = "备份类型")
    private String backupTypeName;

    /**
     * 客户账号
     */
    @ApiModelProperty(value = "客户账号")
    private String customerAccount;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 备份人
     */
    @ApiModelProperty(value = "备份人")
    private String createBy;

    /**
     * 备份方式
     */
    @ApiModelProperty(value = "备份方式")
    private String taskSource;

    /**
     * 备份路径
     */
    @ApiModelProperty(value = "备份路径")
    private String path;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String errorMsg;

    private Boolean latest;
}
